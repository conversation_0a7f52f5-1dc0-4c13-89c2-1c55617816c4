package exchange.pos.service;

import exchange.common.entity.Symbol;
import exchange.common.http.cb.HttpClient;
import exchange.common.util.JsonUtil;
import exchange.pos.model.PosBestPriceData;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.util.Collections;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@RequiredArgsConstructor
public class PosCoinBookBestPriceService {

  @Value("${exchange-pos.best-price.coinbook.api-url}")
  private String apiUrl;

  @Value("${exchange-pos.best-price.coinbook.api-host}")
  private String host;
  public PosBestPriceData getBestPrice(Symbol symbol, Symbol spotSymbol) {
    // 发送HTTP GET请求，获取最佳价格
    HttpClient.HttpResult result =
        HttpClient.httpGet(
                host+apiUrl,
            Collections.emptyList(),
            Map.of("symbolId", String.valueOf(spotSymbol.getId())));
    // 如果返回状态码为500，则记录警告日志并返回null
    if (HttpURLConnection.HTTP_INTERNAL_ERROR == result.code) {
      log.warn("Failed to request API {}. code: {}, msg: {}", host+apiUrl, result.code, result.content);
      return null;
    }

    // 将返回的JSON字符串转换为Map
    String responseJson = result.content;
    Map<String, Object> responseData = JsonUtil.decode(responseJson, Map.class);
    // 如果转换失败，则记录警告日志并返回null
    if (CollectionUtils.isEmpty(responseData)) {
      log.warn("Failed to parse json for get best price of the CoinBook: {}", responseJson);
      return null;
    }

    // 构建PosBestPriceData对象并返回
    return PosBestPriceData.builder()
        .timestamp(System.currentTimeMillis())
        .symbolId(symbol.getId())
        .bestAsk(new BigDecimal(responseData.get("bestAsk").toString()))
        .bestBid(new BigDecimal(responseData.get("bestBid").toString()))
        .build();
  }
}
