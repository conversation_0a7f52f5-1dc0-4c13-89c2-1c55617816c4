package exchange.pos.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.codec.digest.HmacUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import exchange.common.entity.Symbol;
import exchange.common.http.cb.HttpClient;
import exchange.common.util.JsonUtil;
import exchange.pos.model.PosBestPriceData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Transactional(readOnly = true, transactionManager = "masterTransactionManager")
@RequiredArgsConstructor
public class PosAmberBestPriceService {

  @Value("${exchange-pos.best-price.amber.api-host}")
  private String apiHost;

  @Value("${exchange-pos.best-price.amber.api-path}")
  private String apiPath;

  @Value("${exchange-pos.best-price.amber.access-secret}")
  private String accessSecret;

  @Value("${exchange-pos.best-price.amber.access-key}")
  private String accessKey;

  private static final String SIGN_ALGORITHM = "HmacSha256";
  private static final String GET_PRICE_METHOD = "GET";
  private static final String DIRECTION_SELL = "SELL";
  private static final String DIRECTION_BUY = "BUY";
  private static final int TOO_MANY_REQUESTS = 429;

  public PosBestPriceData getBestPrice(Symbol symbol, BigDecimal quantity) {
    
    // 将quantity转换为symbol的基础货币的缩放金额，并向下取整
    quantity =
        symbol.getCurrencyPair().getBaseCurrency().getScaledAmount(quantity, RoundingMode.DOWN);
    // 获取买入价格
    BigDecimal bestAsk =
        this.getPrice(
            quantity, DIRECTION_BUY, symbol.getCurrencyPair().getName());
    // 获取卖出价格
    BigDecimal bestBid =
        this.getPrice(
            quantity, DIRECTION_SELL,symbol.getCurrencyPair().getName());

    // 返回PosBestPriceData对象
    return PosBestPriceData.builder()
        .symbolId(symbol.getId())
        .timestamp(System.currentTimeMillis())
        .bestAsk(bestAsk)
        .bestBid(bestBid)
        .build();
  }

  public BigDecimal getPrice(BigDecimal quantity, String direction, String currencyPair) {
    // 获取当前时间戳用于签名生成
    long timestamp = System.currentTimeMillis();
    
    // 构建请求参数字符串
    String params =
        "quantity=" + quantity.toString() + "&direction=" + direction + "&symbol=" + currencyPair;
    
    // 拼接API路径和参数
    String path = apiPath + "?" + params;
    
    // 创建签名原始字符串
    String signStr = "method=" + GET_PRICE_METHOD + "&path=" + path + "&timestamp=" + timestamp;

    // 使用HMAC-SHA256算法和访问密钥生成签名
    String sign = new HmacUtils(SIGN_ALGORITHM, accessSecret).hmacHex(signStr);

    // 准备请求头信息
    ArrayList<String> headers = new ArrayList<>(6);
    headers.add("access-key");
    headers.add(accessKey);
    headers.add("access-timestamp");
    headers.add(String.valueOf(timestamp));
    headers.add("access-sign");
    headers.add(sign);
    
    // 记录关键请求信息
    log.info("pos amber best price access-key: {} , accessSecret: {}, access-timestamp: {}, sign: {}",accessKey, accessSecret, timestamp, sign);
    
    // 构建完整的请求URL
    String requestUrl = apiHost + path;
    log.info("pos amber request-url:{}",requestUrl);
    
    // 发送HTTP GET请求
    HttpClient.HttpResult result = HttpClient.httpGet(requestUrl, headers, Collections.emptyMap());
    log.info("pos amber repo: content: {}, code: {}", result.content, result.code);
    
    // 处理各种HTTP错误响应
    if (HttpURLConnection.HTTP_INTERNAL_ERROR == result.code) {
      log.warn(
          "Failed to request API {}. code: {}, msg: {},Internal Server Error", requestUrl, result.code, result.content);
      return null;
    }
    if (HttpURLConnection.HTTP_BAD_REQUEST == result.code) {
      log.warn(
          "Failed to request API {}. code: {}, msg: {},Bad request/Request failed", requestUrl, result.code, result.content);
      return null;
    }
    if (HttpURLConnection.HTTP_UNAUTHORIZED == result.code) {
      log.warn(
          "Failed to request API {}. code: {}, msg: {},Unauthorize", requestUrl, result.code, result.content);
      return null;
    }
    if (HttpURLConnection.HTTP_FORBIDDEN == result.code) {
      log.warn(
          "Failed to request API {}. code: {}, msg: {},Forbidden/IP restriction", requestUrl, result.code, result.content);
      return null;
    }
    if (HttpURLConnection.HTTP_NOT_FOUND == result.code) {
      log.warn(
          "Failed to request API {}. code: {}, msg: {},Not found", requestUrl, result.code, result.content);
      return null;
    }
    if (HttpURLConnection.HTTP_UNSUPPORTED_TYPE == result.code) {
      log.warn(
          "Failed to request API {}. code: {}, msg: {},Unsupported media type", requestUrl, result.code, result.content);
      return null;
    }
    if (TOO_MANY_REQUESTS == result.code) {
      log.warn(
          "Failed to request API {}. code: {}, msg: {},Too many requests", requestUrl, result.code, result.content);
      return null;
    }
    
    // 解析JSON响应
    String responseJson = result.content;
    Map<String, Object> responseObj = JsonUtil.decode(responseJson, Map.class);
    if (CollectionUtils.isEmpty(responseObj)) {
      log.warn("Failed to parse json for get best price of the amber: {}", responseJson);
      return null;
    }
    
    // 提取结果对象
    Object resultObj = responseObj.get("result");
    if (Objects.isNull(resultObj)) {
      log.warn("No price returned from amber: {}", responseJson);
      return null;
    }

    // 解析价格数据
    if (resultObj instanceof Map priceObj) {
      // 获取法币参考价格数据
      Map priceValue = (Map) priceObj.get("fiatReference");
      if (Objects.isNull(priceValue)) {
        log.warn("No price returned from amber: {}", responseJson);
        return null;
      }
      
      // 获取日元(JPY)计价的价格
      Object priceJyp = priceValue.get("jpy");
      if (Objects.isNull(priceJyp)) {
        log.warn("No price returned from amber: {}", responseJson);
        return null;
      }
      
      // 将价格转换为BigDecimal类型返回
      return new BigDecimal(priceJyp.toString());
    } else {
      log.warn("The result object does not expected: {}", responseJson);
      return null;
    }
  }

  private String currencyPairMapping(String currencyPair) {
    return currencyPair.replaceAll("JPY", "USDT");
  }
}
