package exchange.pos.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.Objects;
import javax.validation.constraints.NotNull;
import org.springframework.transaction.annotation.Transactional;
import exchange.common.component.CustomRedisTemplate;
import exchange.common.constant.CandlestickType;
import exchange.common.constant.CommonConstants;
import exchange.common.constant.CurrencyPair;
import exchange.common.constant.Exchange;
import exchange.common.constant.TradeType;
import exchange.common.entity.CurrencyPairConfig;
import exchange.common.entity.Symbol;
import exchange.common.service.CurrencyPairConfigService;
import exchange.common.service.SymbolService;
import exchange.common.util.DateUnit;
import exchange.common.websocket.RedisPublisher;
import exchange.pos.entity.PosCandlestick;
import exchange.pos.entity.PosMarketMakerConfig;
import exchange.pos.model.PosBestPriceData;
import exchange.pos.model.PosBestPriceData.PosTicker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Transactional(readOnly = true, transactionManager = "masterTransactionManager")
@RequiredArgsConstructor
public class PosBestPriceService {
  private static final String NIDT = "NIDT";

  private final SymbolService symbolService;
  private final CurrencyPairConfigService currencyPairConfigService;
  private final PosCoinBookBestPriceService coinBookBestPriceService;
  private final PosAmberBestPriceService amberBestPriceService;
  private final CustomRedisTemplate<PosBestPriceData> bestPriceRedisTemplate;
  private final PosBestPriceArchiveService archiveService;
  private final PosMarketMakerConfigService posMarketMakerConfigService;
  private final PosTradeService posTradeService;
  private final PosCandlestickService posCandlestickService;
  private final RedisPublisher redisPublisher;

  public PosBestPriceData getBestPrice(Long symbolId) {
    // get symbol by ID
    Symbol symbol = symbolService.findOne(symbolId);
    if (Objects.isNull(symbol)) {
      log.warn("The symbol not exists by id: {}", symbolId);
      return null;
    }

    if (!TradeType.POS.equals(symbol.getTradeType())) {
      log.warn(
          "The symbol tradeType incorrect, symbolId:{}, symbolName:{}, tradeType: {}",
          symbolId,
          symbol.getBaseCurrencyName(),
          symbol.getTradeType());
      return null;
    }

    Exchange exchange = this.getExchangeBy(symbol.getBaseCurrencyName());
    String cacheKey = this.getCacheKey(exchange, symbol.getName());
    return bestPriceRedisTemplate.getValue(cacheKey);
  }

  public void handleBestPrice(Long symbolId) throws Exception {
    // get symbol by ID
    Symbol symbol = symbolService.findOne(symbolId);
    if (Objects.isNull(symbol)) {
      log.warn("The symbol not exists by id: {}", symbolId);
      return;
    }
    this.handleBestPrice(symbol);
  }

  public void handleBestPrice(@NotNull Symbol symbol) throws Exception {
    // 获取symbol的id
    Long symbolId = symbol.getId();
    // 判断symbol的交易类型是否为POS
    if (!TradeType.POS.equals(symbol.getTradeType())) {
      // 如果不是POS，则记录警告日志
      log.warn(
          "The symbol tradeType incorrect, symbolId:{}, symbolName:{}, tradeType: {}",
          symbolId,
          symbol.getName(),
          symbol.getTradeType());
      return;
    }

    // 根据symbol的基础货币名称获取交易所
    Exchange exchange = this.getExchangeBy(symbol.getBaseCurrencyName());
    // 根据交易类型和货币对获取货币对配置
    CurrencyPairConfig currencyPairConfig = currencyPairConfigService.findOne(TradeType.POS,symbol.getCurrencyPair());
    // 如果货币对配置不可用
    if (!currencyPairConfig.isEnabled()){
      // 获取缓存key
      String cacheKey = this.getCacheKey(exchange, symbol.getName());
      // 删除缓存
      bestPriceRedisTemplate.delete(cacheKey);
      return;
    }
    // -- get best price determine by symbol
    PosBestPriceData bestPrice = this.getBestPrice(symbol);
    if (Objects.isNull(bestPrice)) {
      log.warn("No best price returned by symbol {}", symbol.getName());
      return;
    }

    // 获取当前时间及一天前的时间点
    Date date = new Date();
    long now = date.getTime();
    long before1Day = now - DateUnit.DAY.getMillis();

    // 计算用于查询的起始时间点（分钟和小时粒度）
    Date minute1From =
        CandlestickType.PT1M.getTargetAt(
            DateUnit.MINUTE.truncate(before1Day + DateUnit.MINUTE.getMillis()));
    Date hour1From =
        CandlestickType.PT1H.getTargetAt(
            DateUnit.HOUR.truncate(before1Day + DateUnit.HOUR.getMillis()));

    // 计算用于查询的结束时间点（分钟和小时粒度）
    Date hour1To = CandlestickType.PT1H.getTargetAt(DateUnit.HOUR.truncate(now));
    Date minute1To = CandlestickType.PT1M.getTargetAt(DateUnit.MINUTE.truncate(now));

    // 初始化PosTicker以聚合价格数据
    PosTicker ticker = new PosTicker();

    // 更新ticker数据，使用过去一天到minute1From时间段内的交易数据
    updateTickerByTrades(symbol, ticker, new Date(before1Day), minute1From);

    // 使用minute1From到hour1From时间段内的1分钟K线数据更新ticker
    updateTickerByCandlesticks(symbol, ticker, CandlestickType.PT1M, minute1From, hour1From);

    // 使用hour1From到hour1To时间段内的1小时K线数据更新ticker
    updateTickerByCandlesticks(symbol, ticker, CandlestickType.PT1H, hour1From, hour1To);

    // 使用hour1To到minute1To时间段内的1分钟K线数据更新ticker
    updateTickerByCandlesticks(symbol, ticker, CandlestickType.PT1M, hour1To, minute1To);

    // 更新ticker数据，使用minute1To到当前时间的交易数据
    updateTickerByTrades(symbol, ticker, minute1To, date);

    // 设置最佳价格数据中的symbolId
    bestPrice.setSymbolId(symbol.getId());

    // 对ticker中的开盘价、最高价、最低价和最新价进行精度调整，并设置到bestPrice中
    bestPrice.setOpen(symbol.getCurrencyPair().getScaledPrice(ticker.getOpen()));
    bestPrice.setHigh(symbol.getCurrencyPair().getScaledPrice(ticker.getHigh()));
    bestPrice.setLow(symbol.getCurrencyPair().getScaledPrice(ticker.getLow()));
    bestPrice.setLast(symbol.getCurrencyPair().getScaledPrice(ticker.getLast()));

    // 获取前一天的日期并构造查询时间
    LocalDate currentDay = LocalDate.now();
    LocalDateTime timePrevious = currentDay.minusDays(1).atTime(0, 0, 0);
    ZonedDateTime zonedDateTime = timePrevious.atZone(ZoneId.systemDefault());

    // 查询昨日的日K线数据
    PosCandlestick posCandlestick = posCandlestickService.findOne(symbolId, CandlestickType.P1D,
        Date.from(zonedDateTime.toInstant()));

    // 如果存在日K线数据，则使用其收盘价；否则使用最新交易价作为收盘价
    bestPrice.setClose(
        posCandlestick == null ? symbol.getCurrencyPair().getScaledPrice(ticker.getLast())
            : symbol.getCurrencyPair().getScaledPrice(posCandlestick.getClose()));

    // 对成交量进行精度调整并设置到bestPrice中
    bestPrice.setVolume(
        symbol.getCurrencyPair().getScaledAmount(ticker.getVolume()).toPlainString());

    // 处理买卖价差spread
    this.handleSpread(bestPrice, symbol);

    // 计算中间价(mid)，即买一和卖一的平均值
    BigDecimal mid = bestPrice.getBestAsk().add(bestPrice.getBestBid());
    bestPrice.setMid(symbol.getCurrencyPair().getScaledPrice(mid.divide(new BigDecimal("2"))));

    // 将最新的最佳价格数据同步到Redis缓存中
    bestPriceRedisTemplate.setUnexpireValue(getCacheKey(exchange, symbol.getName()), bestPrice);

    // 通过Redis发布该价格数据，以便WebSocket通知客户端
    redisPublisher.publish(bestPrice);

    // 档案化存储最佳价格数据
    archiveService.archive(symbol, bestPrice.getBestAsk(), bestPrice.getBestBid());
  }

  private void handleSpread(@NotNull PosBestPriceData posBestPriceData, Symbol symbol) {
    // 获取原始最佳买卖价格
    BigDecimal bestAsk = posBestPriceData.getBestAsk();
    BigDecimal bestBid = posBestPriceData.getBestBid();

    // 获取该交易对的配置信息
    CurrencyPairConfig currencyPairConfig =
        currencyPairConfigService.findOne(TradeType.POS, symbol.getCurrencyPair());
    CurrencyPair currencyPair = symbol.getCurrencyPair();

    // 如果货币对配置以及买入和卖出价差都存在
    if (Objects.nonNull(currencyPairConfig)
        && Objects.nonNull(currencyPairConfig.getPosSpreadPercentBuy()) 
        && Objects.nonNull(currencyPairConfig.getPosSpreadPercentSell())) {
      
      // 将百分比转换为小数形式
      BigDecimal posSpreadPercentBuy = currencyPairConfig.getPosSpreadPercentBuy().divide(CommonConstants.HUNDRED);
      BigDecimal posSpreadPercentSell = currencyPairConfig.getPosSpreadPercentSell().divide(CommonConstants.HUNDRED);
      BigDecimal posSlippagePercent = currencyPairConfig.getPosSlippagePercent().divide(CommonConstants.HUNDRED);

      // 根据公式计算调整后的最佳买卖价格
      // ask be: MM_price * (1 + Spread + Slippage)
      BigDecimal bestAskMultiply = bestAsk.multiply((BigDecimal.ONE.add(posSpreadPercentBuy).add(posSlippagePercent)));
      // bid be: MM_price * (1 - Spread - Slippage)
      BigDecimal bestBidMultiply = bestBid.multiply(BigDecimal.ONE.subtract(posSpreadPercentSell).subtract(posSlippagePercent));

      // 对价格进行精度调整并设置到posBestPriceData中
      posBestPriceData.setBestAsk(bestAskMultiply.setScale(currencyPair.getQuotePrecision(), RoundingMode.UP));
      posBestPriceData.setBestBid(bestBidMultiply.setScale(currencyPair.getQuotePrecision(), RoundingMode.DOWN));
    }

    // 设置市场做市商原始的最佳买卖价格
    posBestPriceData.setBestMmAsk(bestAsk);
    posBestPriceData.setBestMmBid(bestBid);
  }

  private PosBestPriceData getBestPrice(Symbol symbol) {
    // 判断基础货币名称是否为NIDT
    if (NIDT.equalsIgnoreCase(symbol.getBaseCurrencyName())) {
      // 根据交易类型和货币对查找现货交易对
      Symbol spotSymbol =
          symbolService.findByConditionWithCache(TradeType.SPOT, symbol.getCurrencyPair());
      // 如果没有找到现货交易对，则记录警告日志并返回null
      if (Objects.isNull(spotSymbol)) {
        log.warn("No spot symbol found by tradeType=SPOT and {}", symbol.getCurrencyPair());
        return null;
      }

      // 调用coinBookBestPriceService的getBestPrice方法，传入symbol和spotSymbol，返回最佳价格数据
      return coinBookBestPriceService.getBestPrice(symbol, spotSymbol);
    } else {
      // 根据symbolId查找PosMarketMakerConfig
      PosMarketMakerConfig marketMakerConfig =
          posMarketMakerConfigService.findBySymbolId(symbol.getId());
      // 如果没有找到PosMarketMakerConfig，则记录警告日志并返回null
      if (Objects.isNull(marketMakerConfig)) {
        log.warn(
            "No PosMarketMakerConfig found by symbolId: {} or config is disabled", symbol.getId());
        return null;
      }
      // 调用amberBestPriceService的getBestPrice方法，传入symbol和quantity，返回最佳价格数据
      return amberBestPriceService.getBestPrice(symbol, marketMakerConfig.getQuantity());
    }
  }

  /**
   * @param exchange
   * @param symbolName POS_NIDT_JPY or POS_ETH_JPY
   * @return
   */
  private String getCacheKey(Exchange exchange, String symbolName) {
    return ("best_price:" + exchange.getName() + ":" + symbolName).toLowerCase();
  }

  /**
   * @param baseCurrencyName NIDT or BTC or ETH ...
   * @return
   */
  private Exchange getExchangeBy(String baseCurrencyName) {
    return NIDT.equalsIgnoreCase(baseCurrencyName) ? Exchange.COINBOOK : Exchange.AMBER;
  }

  private void updateTicker(
      PosTicker ticker,
      BigDecimal open,
      BigDecimal high,
      BigDecimal low,
      BigDecimal last,
      BigDecimal volume) {
    if (ticker.getOpen().signum() == 0) {
      ticker.setOpen(open);
    }
    if (ticker.getHigh().signum() == 0 || ticker.getHigh().compareTo(high) < 0) {
      ticker.setHigh(high);
    }

    if (ticker.getLow().signum() == 0 || ticker.getLow().compareTo(low) > 0) {
      ticker.setLow(low);
    }

    ticker.setLast(last);
    ticker.setVolume(ticker.getVolume().add(volume));
  }

  private void updateTickerByTrades(Symbol symbol, PosTicker ticker, Date dateFrom, Date dateTo)
      throws Exception {
    // Takerには販売所のスプレッドが含まれるため、Makerで生成する
    // 根据条件查找Maker交易
    posTradeService
        .findMakerByCondition(symbol.getId(), dateFrom, dateTo)
        // 遍历Maker交易
        .forEach(
            spotTrade ->
                // 更新PosTicker
                updateTicker(
                    ticker,
                    spotTrade.getPrice(),
                    spotTrade.getPrice(),
                    spotTrade.getPrice(),
                    spotTrade.getPrice(),
                    spotTrade.getAmount()));
  }

  private void updateTickerByCandlesticks(
      Symbol symbol, PosTicker ticker, CandlestickType candlestickType, Date from, Date to) {
    posCandlestickService
        .findByCondition(symbol.getId(), candlestickType, from, to)
        .forEach(
            candlestick ->
                updateTicker(
                    ticker,
                    candlestick.getOpen(),
                    candlestick.getHigh(),
                    candlestick.getLow(),
                    candlestick.getClose(),
                    candlestick.getVolume()));
  }
}
