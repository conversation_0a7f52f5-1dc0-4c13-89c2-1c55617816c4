package exchange.pos.service;

import exchange.common.component.HistoricalTransactionManager;
import exchange.common.entity.Symbol;
import java.math.BigDecimal;
import java.sql.Types;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.ArgumentTypePreparedStatementSetter;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@Transactional(readOnly = true, transactionManager = "historicalJpaTransactionManager")
@RequiredArgsConstructor
public class PosBestPriceArchiveService {
  private static final String ARCHIVE_SQL =
      "insert into pos_best_price (symbol_id, best_ask, best_bid) values (?, ?, ?)";

  private final HistoricalTransactionManager historicalTransactionManager;

  @Transactional(rollbackFor = Exception.class, transactionManager = "historicalJpaTransactionManager")
  public void archive(Symbol symbol, BigDecimal bestAsk, BigDecimal bestBid) {

    // 如果bestAsk或bestBid为空，则直接返回
    if (Objects.isNull(bestAsk) || Objects.isNull(bestBid)) {
      return;
    }

    // 创建一个ArgumentTypePreparedStatementSetter对象，用于设置参数
    ArgumentTypePreparedStatementSetter archiveParameter =
        new ArgumentTypePreparedStatementSetter(
            new Object[] {symbol.getId(), bestAsk, bestBid},
            new int[] {Types.BIGINT, Types.NUMERIC, Types.NUMERIC});
    // 使用historicalTransactionManager对象执行archiveWithParameter方法，传入SQL语句和参数
    historicalTransactionManager.archiveWithParameter(ARCHIVE_SQL, archiveParameter);
    // 记录日志
    log.info(
        "archive_log_pos_best_price,symbolId,{},symbolName,{}", symbol.getId(), symbol.getName());
  }
}
