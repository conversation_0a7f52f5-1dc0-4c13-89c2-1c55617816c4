package exchange.pos.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.constant.CandlestickType;
import exchange.common.entity.Candlestick_;
import exchange.common.entity.Symbol;
import exchange.common.service.EntityService;
import exchange.common.util.JsonUtil;
import exchange.pos.entity.PosCandlestick;
import exchange.pos.entity.PosTrade;
import exchange.pos.predicate.PosCandlestickPredicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class PosCandlestickService extends EntityService<PosCandlestick, PosCandlestickPredicate> {

  private final PosTradeService posTradeService;

  @Override
  public Class<PosCandlestick> getEntityClass() {
    return PosCandlestick.class;
  }

  private String getCacheKey(Long symbolId, CandlestickType candlestickType, Date targetAt) {
    return "candlestick:" + symbolId + ":" + candlestickType + ":" + targetAt.getTime();
  }

  @Override
  protected void saveCache(PosCandlestick candlestick) {
    super.saveCache(candlestick);
    redisTemplate.setValue(getCacheKey(candlestick.getSymbolId()), candlestick);
  }

  @Override
  protected void deleteCache(PosCandlestick candlestick) {
    super.deleteCache(candlestick);
    redisTemplate.delete(getCacheKey(candlestick.getSymbolId()));
  }
  protected List<Predicate> getIndexedPredicates(CriteriaBuilder criteriaBuilder,
      Root<PosCandlestick> root, Long symbolId, CandlestickType candlestickType) {
    List<Predicate> predicates = new ArrayList<>();
    predicates.add(predicate.equalSymbolId(criteriaBuilder, root, symbolId));
    predicates.add(predicate.equalCandlestickType(criteriaBuilder, root, candlestickType));
    return predicates;
  }

  public PosCandlestick findOne(Long symbolId, CandlestickType candlestickType, Date targetAt) {
    // 定义一个PosCandlestick类型的变量candlestick，初始值为null
    PosCandlestick candlestick = null;

    try {
      // 从缓存中获取candlestick
      candlestick = redisTemplate.getValue(getCacheKey(symbolId, candlestickType, targetAt));
    } catch (Exception e) {
      // 如果出现异常，则不做任何处理
      // do nothing
    }

    // 如果candlestick为null，则从数据库中查找
    if (candlestick == null) {
      // 使用自定义的事务管理器查找candlestick
      candlestick =
          customTransactionManager.find(
              getEntityClass(),
              new QueryExecutorReturner<PosCandlestick, PosCandlestick>() {
                @Override
                public PosCandlestick query() {
                  // 获取查询条件
                  List<Predicate> predicates =
                      getIndexedPredicates(criteriaBuilder, root, symbolId, candlestickType);
                  // 添加目标时间条件
                  predicates.add(predicate.equalTargetAt(criteriaBuilder, root, targetAt));
                  // 返回查询结果
                  return getSingleResult(entityManager, criteriaQuery, root, predicates);
                }
              });

      // 如果查询结果不为null，则将结果保存到缓存中
      if (candlestick != null) {
        saveCache(candlestick);
      }
    }

    // 返回查询结果
    return candlestick;
  }

  public PosCandlestick findOrCreate(Long symbolId, CandlestickType candlestickType,
      Date targetAt) {
    // 根据symbolId、candlestickType和targetAt查找PosCandlestick
    PosCandlestick candlestick = findOne(symbolId, candlestickType, targetAt);

    // 如果找不到，则创建一个新的PosCandlestick
    if (candlestick == null) {
      candlestick = newEntity();
      // 设置PosCandlestick的属性
      candlestick.setProperties(symbolId, candlestickType, targetAt);
    }

    // 返回PosCandlestick
    return candlestick;
  }

  public PosCandlestick findLatest(Long symbolId, CandlestickType candlestickType, Date targetAt) {
    return customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<PosCandlestick, PosCandlestick>() {
          @Override
          public PosCandlestick query() {
            List<Predicate> predicates = getIndexedPredicates(criteriaBuilder, root, symbolId,
                candlestickType);
            predicates.add(predicate.lessThanTargetAt(criteriaBuilder, root, targetAt));
            return getSingleResult(entityManager, criteriaQuery, root, predicates,
                criteriaBuilder.desc(root.get(Candlestick_.targetAt)));
          }
        });
  }

  public List<PosCandlestick> findByCondition(
      Long symbolId, CandlestickType candlestickType, Date dateFrom, Date dateTo) {
    // 使用自定义的事务管理器查找满足条件的PosCandlestick列表
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<PosCandlestick, List<PosCandlestick>>() {
          @Override
          public List<PosCandlestick> query() {
            // 获取满足条件的谓词列表
            List<Predicate> predicates =
                getIndexedPredicates(criteriaBuilder, root, symbolId, candlestickType);

            // 如果dateFrom不为空，则添加大于等于dateFrom的谓词
            if (dateFrom != null) {
              predicates.add(
                  predicate.greaterThanOrEqualToTargetAt(criteriaBuilder, root, dateFrom));
            }

            // 如果dateTo不为空，则添加小于dateTo的谓词
            if (dateTo != null) {
              predicates.add(predicate.lessThanTargetAt(criteriaBuilder, root, dateTo));
            }

            // 返回满足条件的PosCandlestick列表
            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                0,
                Integer.MAX_VALUE,
                criteriaBuilder.asc(root.get(Candlestick_.targetAt)));
          }
        });
  }

  public List<PosCandlestick> findByCondition(
      Long symbolId, CandlestickType candlestickType, Date dateFrom, Date dateTo, Integer num) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<PosCandlestick, List<PosCandlestick>>() {
          @Override
          public List<PosCandlestick> query() {
            List<Predicate> predicates =
                getIndexedPredicates(criteriaBuilder, root, symbolId, candlestickType);

            if (dateFrom != null) {
              predicates.add(
                  predicate.greaterThanOrEqualToTargetAt(criteriaBuilder, root, dateFrom));
            }

            if (dateTo != null) {
              predicates.add(predicate.lessThanTargetAt(criteriaBuilder, root, dateTo));
            }

            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                0,
                num,
                criteriaBuilder.desc(root.get(Candlestick_.targetAt)));
          }
        });
  }


  public void make(Symbol symbol, CandlestickType candlestickType, Date targetAt) {
    // 设置目标时间为指定的时间
    targetAt = candlestickType.getTargetAt(targetAt);
    // 查找最新的蜡烛图
    PosCandlestick previous = findLatest(symbol.getId(), candlestickType, targetAt);
    // 打印最新的蜡烛图
    log.info("maker date: {} posCandlestick: {}",targetAt ,JsonUtil.encode(previous));
    // 如果最新的蜡烛图存在且未固定
    if (previous != null && !previous.isFixed()) {
      // 查找更早的蜡烛图
      PosCandlestick morePrevious =
          findPrevious(
              previous.getSymbolId(), previous.getCandlestickType(), previous.getTargetAt());

      // 如果蜡烛图类型为PT1M
      if (candlestickType == CandlestickType.PT1M) {
        // 制作1分钟的蜡烛图
        makeMinute1(symbol, previous, morePrevious);
      } else {
        // 制作非1分钟的蜡烛图
        makeWithoutMinute1(symbol, previous, morePrevious);
      }
    }
    // 查找或创建蜡烛图
    PosCandlestick candlestick = findOrCreate(symbol.getId(), candlestickType, targetAt);

    // 如果蜡烛图类型为PT1M
    if (candlestickType == CandlestickType.PT1M) {
      // 制作1分钟的蜡烛图
      makeMinute1(symbol, candlestick, previous);
    } else {
      // 制作非1分钟的蜡烛图
      makeWithoutMinute1(symbol, candlestick, previous);
    }

  }

  private PosCandlestick findPrevious(Long symbolId, CandlestickType candlestickType,
      Date targetAt) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<PosCandlestick, PosCandlestick>() {
          @Override
          public PosCandlestick query() {
            List<Predicate> predicates =
                getIndexedPredicates(criteriaBuilder, root, symbolId, candlestickType);
            predicates.add(predicate.lessThanTargetAt(criteriaBuilder, root, targetAt));
            return getSingleResult(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                criteriaBuilder.desc(root.get(Candlestick_.targetAt)));
          }
        });
  }

  private void makeMinute1(Symbol symbol, PosCandlestick candlestick, PosCandlestick previous) {
    // 记录日志，输出candlestick的目标时间和下一个目标时间
    log.info("makeMinute1 fromTargetAt:{},toTargetAt:{}",candlestick.getTargetAt(),candlestick.getCandlestickType().getNextTargetAt(candlestick.getTargetAt()));
    // Takerには販売所のスプレッドが含まれるため、Makerで生成する
    // 根据symbol的id和candlestick的目标时间和下一个目标时间，查找Maker的交易
    List<PosTrade> posTrades =
        posTradeService
            .findMakerByCondition(
                symbol.getId(),
                candlestick.getTargetAt(),
                candlestick.getCandlestickType().getNextTargetAt(candlestick.getTargetAt()));
    // 重置candlestick
    candlestick.reset();

    // 如果没有找到Maker的交易，则根据previous更新candlestick
    if (CollectionUtils.isEmpty(posTrades)) {
      updateByPrevious(candlestick, previous);
    } else {
      // 如果找到了Maker的交易，则更新candlestick
      posTrades.forEach(
          cxrTradeData ->
              candlestick.update(
                  cxrTradeData.getPrice(),
                  cxrTradeData.getPrice(),
                  cxrTradeData.getPrice(),
                  cxrTradeData.getPrice(),
                  cxrTradeData.getAmount()));
    }

    // 如果当前时间已经超过了candlestick的下一个目标时间，则设置candlestick为固定
    if (new Date()
        .after(candlestick.getCandlestickType().getNextTargetAt(candlestick.getTargetAt()))) {
      candlestick.setFixed(true);
    }

    // 保存candlestick
    save(candlestick);
  }

  protected void updateByPrevious(PosCandlestick candlestick, PosCandlestick previous) {
    if (previous == null) {
      return;
    }

    candlestick.update(
        previous.getClose(),
        previous.getClose(),
        previous.getClose(),
        previous.getClose(),
        BigDecimal.ZERO);
  }

// 根据给定的symbol、candlestick和previous，生成一个没有分钟的PosCandlestick
  private void makeWithoutMinute1(Symbol symbol, PosCandlestick candlestick,
      PosCandlestick previous) {
    // 获取下一个目标时间
    Date nextTargetAt = candlestick.getCandlestickType().getNextTargetAt(candlestick.getTargetAt());
    // 根据条件查找PosCandlestick
    List<PosCandlestick> elements =
        findByCondition(
            candlestick.getSymbolId(),
            candlestick.getCandlestickType().getElementType(),
            candlestick.getTargetAt(),
            nextTargetAt);
    // 重置candlestick
    candlestick.reset();

    // 如果没有找到符合条件的PosCandlestick，则根据previous更新candlestick
    if (CollectionUtils.isEmpty(elements)) {
      updateByPrevious(candlestick, previous);
    } else {
      // 否则，根据找到的PosCandlestick更新candlestick
      elements
          .stream()
          .forEach(
              element ->
                  candlestick.update(
                      element.getOpen(),
                      element.getHigh(),
                      element.getLow(),
                      element.getClose(),
                      element.getVolume()));
    }

    // 如果当前时间超过了下一个目标时间，则设置candlestick为固定
    if (new Date().after(nextTargetAt)) {
      candlestick.setFixed(true);
    }

    // 保存candlestick
    save(candlestick);
  }
  
  public PosCandlestick findOneByCondition(
	      Long symbolId,
	      CandlestickType candlestickType,
	      Long dateFrom,
	      Long dateTo,
	      boolean isAscending, boolean isEnable) {
	    return customTransactionManager.find(
	        getEntityClass(),
	        new QueryExecutorReturner<PosCandlestick, PosCandlestick>() {
	          @Override
	          public PosCandlestick query() {
	            List<Predicate> predicates =
	                getIndexedPredicates(criteriaBuilder, root, symbolId, candlestickType);

	            if (dateFrom != null) {
	              predicates.add(
	                  predicate.greaterThanOrEqualToTargetAt(
	                      criteriaBuilder, root, new Date(dateFrom)));
	            }

	            if (dateTo != null) {
	              predicates.add(predicate.lessThanTargetAt(criteriaBuilder, root, new Date(dateTo)));
	            }
	            
	            if (!isEnable) {
	              // 無効な場合、OkCoinから取得した終値（open=0）を取得
                  predicates.add(predicate.equalOpen(criteriaBuilder, root, new BigDecimal(0)));
	            }

	            return getSingleResult(
	                entityManager,
	                criteriaQuery,
	                root,
	                predicates,
	                isAscending
	                    ? criteriaBuilder.asc(root.get(Candlestick_.targetAt))
	                    : criteriaBuilder.desc(root.get(Candlestick_.targetAt)));
	          }
	        });
  }
  
  public PosCandlestick findOneByConditionForJpyConversion(
      Long symbolId,
      CandlestickType candlestickType,
      Long dateFrom,
      Long dateTo,
      boolean isAscending) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<PosCandlestick, PosCandlestick>() {
          @Override
          public PosCandlestick query() {
            List<Predicate> predicates =
                getIndexedPredicates(criteriaBuilder, root, symbolId, candlestickType);

            if (dateFrom != null) {
              predicates.add(
                  predicate.greaterThanOrEqualToUpdatedAt(
                      criteriaBuilder, root, new Date(dateFrom)));
            }

            if (dateTo != null) {
              predicates.add(predicate.lessThanUpdatedAt(criteriaBuilder, root, new Date(dateTo)));
            }

            // OkCoinから取得した終値
            predicates.add(predicate.equalOpen(criteriaBuilder, root, new BigDecimal(0)));
            return getSingleResult(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                isAscending
                    ? criteriaBuilder.asc(root.get(Candlestick_.targetAt))
                    : criteriaBuilder.desc(root.get(Candlestick_.targetAt)));
          }
        });
}

  @Override
  public void redisPublish(PosCandlestick entity){
    redisPublisher.publish(entity);
  }
}
