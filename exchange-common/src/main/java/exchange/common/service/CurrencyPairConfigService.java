package exchange.common.service;

import java.util.ArrayList;
import java.util.List;
import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Service;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.constant.CurrencyPair;
import exchange.common.constant.TradeType;
import exchange.common.entity.CurrencyPairConfig;
import exchange.common.entity.CurrencyPairConfig_;
import exchange.common.predicate.CurrencyPairConfigPredicate;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class CurrencyPairConfigService
    extends EntityService<CurrencyPairConfig, CurrencyPairConfigPredicate> {

  @Override
  public Class<CurrencyPairConfig> getEntityClass() {
    return CurrencyPairConfig.class;
  }

  private List<Predicate> getPredicatesOfFindByCondition(
      CriteriaBuilder criteriaBuilder,
      Root<CurrencyPairConfig> root,
      TradeType tradeType,
      CurrencyPair currencyPair) {
    List<Predicate> predicates = new ArrayList<>();

    if (tradeType != null) {
      predicates.add(predicate.equalTradeType(criteriaBuilder, root, tradeType));
    }

    if (currencyPair != null) {
      predicates.add(predicate.equalCurrencyPair(criteriaBuilder, root, currencyPair));
    }

    return predicates;
  }

  public CurrencyPairConfig findByCondition(TradeType tradeType, CurrencyPair currencyPair) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<CurrencyPairConfig, CurrencyPairConfig>() {
          @Override
          public CurrencyPairConfig query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.equalTradeType(criteriaBuilder, root, tradeType));
            predicates.add(predicate.equalCurrencyPair(criteriaBuilder, root, currencyPair));
            return getSingleResult(entityManager, criteriaQuery, root, predicates);
          }
        });
  }

  public CurrencyPairConfig findByCondition(
      TradeType tradeType, CurrencyPair currencyPair, EntityManager entityManager) {
    return new QueryExecutorReturner<CurrencyPairConfig, CurrencyPairConfig>() {
      @Override
      public CurrencyPairConfig query() {
        List<Predicate> predicates = new ArrayList<>();
        predicates.add(predicate.equalTradeType(criteriaBuilder, root, tradeType));
        predicates.add(predicate.equalCurrencyPair(criteriaBuilder, root, currencyPair));
        return getSingleResult(entityManager, criteriaQuery, root, predicates);
      }
    }.execute(getEntityClass(), entityManager);
  }

  public List<CurrencyPairConfig> findAllByCondition(
      TradeType tradeType, CurrencyPair currencyPair, boolean enabled) {
    List<CurrencyPairConfig> currencyPairConfig =
        customTransactionManager.find(
            getEntityClass(),
            new QueryExecutorReturner<CurrencyPairConfig, List<CurrencyPairConfig>>() {

              @Override
              public List<CurrencyPairConfig> query() {
                List<Predicate> predicates =
                    getPredicatesOfFindByCondition(criteriaBuilder, root, tradeType, currencyPair);
                predicates.add(predicate.isEnabled(criteriaBuilder, root, enabled));

                return getResultList(
                    entityManager,
                    criteriaQuery,
                    root,
                    predicates,
                    criteriaBuilder.asc(root.get(CurrencyPairConfig_.id)));
              }
            });

    return currencyPairConfig;
  }

  public List<CurrencyPairConfig> findAllByCondition(
      TradeType tradeType, CurrencyPair currencyPair) {
    List<CurrencyPairConfig> currencyPairConfig =
        customTransactionManager.find(
            getEntityClass(),
            new QueryExecutorReturner<CurrencyPairConfig, List<CurrencyPairConfig>>() {

              @Override
              public List<CurrencyPairConfig> query() {
                List<Predicate> predicates =
                    getPredicatesOfFindByCondition(criteriaBuilder, root, tradeType, currencyPair);
                return getResultList(
                    entityManager,
                    criteriaQuery,
                    root,
                    predicates,
                    criteriaBuilder.asc(root.get(CurrencyPairConfig_.id)));
              }
            });

    return currencyPairConfig;
  }

  public CurrencyPairConfig findOne(TradeType tradeType, CurrencyPair currencyPair) {
    // 定义一个CurrencyPairConfig对象，初始值为null
    CurrencyPairConfig currencyPairConfig = null;
    // 获取缓存的key
    String key = getCacheKey(tradeType, currencyPair);
    try {
      // 从缓存中获取CurrencyPairConfig对象
      currencyPairConfig = redisTemplate.getValue(key);
    } catch (Exception e) {
      // 如果获取失败，记录警告日志
      log.warn("redisTemplate.getValue() is missing. key: " + key);
    }

    // 如果缓存中没有CurrencyPairConfig对象，则从数据库中获取
    if (currencyPairConfig == null) {
      currencyPairConfig = findByCondition(tradeType, currencyPair);
      // 如果从数据库中获取到了CurrencyPairConfig对象，则将其保存到缓存中
      if (currencyPairConfig != null) {
        saveCache(currencyPairConfig);
      }
    }
    // 返回CurrencyPairConfig对象
    return currencyPairConfig;
  }

  private String getCacheKey(CurrencyPairConfig currencyPairConfig) {
    return getCacheKey(currencyPairConfig.getTradeType(), currencyPairConfig.getCurrencyPair());
  }

  private String getCacheKey(TradeType tradeType, CurrencyPair currencyPair) {
    return "currencyPairConfig:" + tradeType.name() + ":" + currencyPair.name();
  }

  @Override
  public void saveCache(CurrencyPairConfig currencyPairConfig) {
    super.saveCache(currencyPairConfig);
    redisTemplate.setValue(getCacheKey(currencyPairConfig), currencyPairConfig);
  }

  @Override
  public CurrencyPairConfig save(CurrencyPairConfig currencyPairConfig) {
    super.save(currencyPairConfig);
    redisTemplate.delete(getCacheKey(currencyPairConfig));
    return currencyPairConfig;
  }

  @Override
  public CurrencyPairConfig save(CurrencyPairConfig currencyPairConfig, EntityManager entityManager)
      throws Exception {
    super.save(currencyPairConfig, entityManager);
    redisTemplate.delete(getCacheKey(currencyPairConfig));
    return currencyPairConfig;
  }

  @Override
  public void deleteCache(CurrencyPairConfig currencyPairConfig) {
    super.deleteCache(currencyPairConfig);
    redisTemplate.delete(getCacheKey(currencyPairConfig));
  }

  @Override
  public void delete(CurrencyPairConfig currencyPairConfig) {
    super.delete(currencyPairConfig);
    redisTemplate.delete(getCacheKey(currencyPairConfig));
  }

  @Override
  public void delete(CurrencyPairConfig currencyPairConfig, EntityManager entityManager) {
    super.delete(currencyPairConfig, entityManager);
    redisTemplate.delete(getCacheKey(currencyPairConfig));
  }

  @Override
  public void redisPublish(CurrencyPairConfig entity) {
    redisPublisher.publish(entity);
  }
}
