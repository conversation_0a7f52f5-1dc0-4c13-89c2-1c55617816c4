package exchange.common.websocket;

import exchange.common.component.CustomRedisTemplate;
import exchange.common.entity.Asset;
import exchange.common.entity.Candlestick;
import exchange.common.entity.CurrencyPairConfig;
import exchange.common.model.response.TickerData;
import exchange.common.model.response.TradesData;
import exchange.common.model.response.websocket.*;
import exchange.common.model.response.OrderbookData;
import exchange.pos.entity.PosCandlestick;
import exchange.pos.entity.PosTrade;
import exchange.pos.model.PosBestPriceData;
import exchange.spot.entity.SpotOrder;
import exchange.spot.entity.SpotTrade;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@Slf4j
public class RedisPublisher {
  private final CustomRedisTemplate<String> redisTemplate;
  private final CustomRedisTemplate<OrderbookDataWrapper> orderBookDataRedisTemplate;
  private final CustomRedisTemplate<CandlestickDataWrapper> candlestickDataRedisTemplate;
  private final CustomRedisTemplate<AssetDataWrapper> assetDataRedisTemplate;
  private final CustomRedisTemplate<SpotOrderDataWrapper> orderDataRedisTemplate;
  private final CustomRedisTemplate<SpotTradeDataWrapper> tradeDataRedisTemplate;
  private final CustomRedisTemplate<TickerDataWrapper> tickerDataRedisTemplate;
  private final CustomRedisTemplate<TradesDataWrapper> tradesDataRedisTemplate;
  private final CustomRedisTemplate<CurrencyPairConfigDataWrapper> currencyPairConfigTemplate;
  private final CustomRedisTemplate<PosCandlestickDataWrapper> posCandlestickDataRedisTemplate;
  private final CustomRedisTemplate<PosBestPriceData> posBestPriceDataRedisTemplate;
  private final CustomRedisTemplate<PosTradeDataWrapper> posTradePriceDataRedisTemplate;

  private final ChannelTopic orderBookTopic;
  private final ChannelTopic candlestickTopic;
  private final ChannelTopic assetTopic;
  private final ChannelTopic spotOrderUpdateTopic;
  private final ChannelTopic spotTradeUpdateTopic;
  private final ChannelTopic tickerTopic;
  private final ChannelTopic tradesTopic;
  private final ChannelTopic currencyPairTopic;
  private final ChannelTopic posCandlestickTopic;
  private final ChannelTopic posPriceTopic;
  private final ChannelTopic posTradeUpdateTopic;

  public boolean websocketEnabled = true;
  @Value("${exchange-websocket.redis-pubsub-cache.expire-in-minutes:5}")
  public int cacheExpireMinutes;
  @Value("${exchange-websocket.redis-pubsub-cache.enabled:true}")
  public boolean cacheEnabled;

  public void publish(final OrderbookData entity) {
    if (isWebsocketDisabled()) {
      return;
    }
    OrderbookDataWrapper dataWrapper = new OrderbookDataWrapper(entity);
    String cacheKey = dataWrapper.getCacheKey();
    String checksum = dataWrapper.getChecksum();
    if (cached(cacheKey, checksum)) {
      log.debug("Skip sending to Redis for " + cacheKey + " checksum:" + checksum);
      return;
    }
    log.info("Do sending to Redis for " + cacheKey + " checksum:" + checksum);
    orderBookDataRedisTemplate.convertAndSend(orderBookTopic.getTopic(), dataWrapper);
  }

  public void publish(final Candlestick entity) {
    if (isWebsocketDisabled()) {
      return;
    }
    CandlestickDataWrapper dataWrapper = new CandlestickDataWrapper(entity);
    String cacheKey = dataWrapper.getCacheKey();
    String checksum = dataWrapper.getChecksum();
    if (cached(cacheKey, checksum)) {
      log.debug("Skip sending to Redis for " + cacheKey + " checksum:" + checksum);
      return;
    }
    log.info("Do sending to Redis for " + cacheKey + " checksum:" + checksum);
    candlestickDataRedisTemplate.convertAndSend(candlestickTopic.getTopic(), new CandlestickDataWrapper(entity));
  }

  public void publish(final Asset entity) {
    if (isWebsocketDisabled()) {
      return;
    }
    // No cache
    assetDataRedisTemplate.convertAndSend(assetTopic.getTopic(), new AssetDataWrapper(entity));
  }

  public void publish(final SpotOrder entity) {
    if (isWebsocketDisabled()) {
      return;
    }
    // No cache
    orderDataRedisTemplate.convertAndSend(spotOrderUpdateTopic.getTopic(), new SpotOrderDataWrapper(entity));
  }

  public void publish(final SpotTrade entity) {
    if (isWebsocketDisabled()) {
      return;
    }
    // No cache
    tradeDataRedisTemplate.convertAndSend(spotTradeUpdateTopic.getTopic(), new SpotTradeDataWrapper(entity));
  }

  public void publish(final TickerData entity) {
    if (isWebsocketDisabled()) {
      return;
    }
    TickerDataWrapper dataWrapper = new TickerDataWrapper(entity);
    String cacheKey = dataWrapper.getCacheKey();
    String checksum = dataWrapper.getChecksum();
    if (cached(cacheKey, checksum)) {
      log.debug("Skip sending to Redis for " + cacheKey + " checksum:" + checksum);
      return;
    }
    log.info("Do sending to Redis for " + cacheKey + " checksum:" + checksum);
    tickerDataRedisTemplate.convertAndSend(tickerTopic.getTopic(), dataWrapper);
  }

  public void publish(final TradesData entity) {
    if (isWebsocketDisabled()) {
      return;
    }
    TradesDataWrapper dataWrapper = new TradesDataWrapper(entity);
    String cacheKey = dataWrapper.getCacheKey();
    String checksum = dataWrapper.getChecksum();
    if (cached(cacheKey, checksum)) {
      log.debug("Skip sending to Redis for " + cacheKey + " checksum:" + checksum);
      return;
    }
    log.info("Do sending to Redis for " + cacheKey + " checksum:" + checksum);
    tradesDataRedisTemplate.convertAndSend(tradesTopic.getTopic(), dataWrapper);
  }

  public void publish(final CurrencyPairConfig entity) {
    if (isWebsocketDisabled()) {
      return;
    }
    // No cache
    currencyPairConfigTemplate.convertAndSend(currencyPairTopic.getTopic(), new CurrencyPairConfigDataWrapper(entity));
  }

  public void publish(final PosCandlestick entity) {
    if (isWebsocketDisabled()) {
      return;
    }
    PosCandlestickDataWrapper dataWrapper = new PosCandlestickDataWrapper(entity);
    String cacheKey = dataWrapper.getCacheKey();
    String checksum = dataWrapper.getChecksum();
    if (cached(cacheKey, checksum)) {
      log.debug("Skip sending to Redis for " + cacheKey + " checksum:" + checksum);
      return;
    }
    log.info("Do sending to Redis for " + cacheKey + " checksum:" + checksum);
    posCandlestickDataRedisTemplate.convertAndSend(posCandlestickTopic.getTopic(), new PosCandlestickDataWrapper(entity));
  }

  public void publish(final PosBestPriceData entity) {
    // 如果websocket被禁用，则直接返回
    if (isWebsocketDisabled()) {
      return;
    }
    // 创建PosBestPriceDataWrapper对象
    PosBestPriceDataWrapper dataWrapper = new PosBestPriceDataWrapper(entity);
    // 获取缓存key
    String cacheKey = dataWrapper.getCacheKey();
    // 获取校验和
    String checksum = dataWrapper.getChecksum();
    // 如果缓存中已经存在该数据，则直接返回
    if (cached(cacheKey, checksum)) {
      log.debug("Skip sending to Redis for " + cacheKey + " checksum:" + checksum);
      return;
    }
    // 否则，将数据发送到Redis
    log.info("Do sending to Redis for " + cacheKey + " checksum:" + checksum);

    posBestPriceDataRedisTemplate.convertAndSend(posPriceTopic.getTopic(), entity);
  }

  public void publish(final PosTrade entity) {
    if (isWebsocketDisabled()) {
      return;
    }
    // No cache
    posTradePriceDataRedisTemplate.convertAndSend(posTradeUpdateTopic.getTopic(), new PosTradeDataWrapper(entity));
  }

  // -- helper method
  private boolean isWebsocketDisabled() {
    boolean disabled = !websocketEnabled;
    if (disabled) {
      log.debug("WebSocket is disabled on this server.");
    }
    return disabled;
  }

  private boolean cached(String cacheKey, String checksum) {
    if (!cacheEnabled) {
      log.debug("WebSocket cached is disabled on this server.");
      return false;
    }
    if (checksum == null) {
      return false;
    }
    return !redisTemplate.setIfUncached(cacheKey, checksum, cacheExpireMinutes);
  }
}
