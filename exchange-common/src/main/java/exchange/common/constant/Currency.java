package exchange.common.constant;

import lombok.Getter;

import java.math.BigDecimal;
import java.math.RoundingMode;

// 有効な通貨一覧を取得する場合はcurrencyConfig(enabled=true)を使用すること
// currencyConfigの無効(enabled=false)通貨もここで保持すること。ここのみに存在する分は問題なし
public enum Currency {
  // fiat
  JPY("Japanese Yen", 0, "", ""),
  // crypto
  ADA("Cardano", 6, "cardano-preprod", "Cardano"),
  // IEO
  NIDT("Nippon Idol Token", 8, "Nippon-Idol-Token", "Ethereum"),
  BTC("Bitcoin", 8, "", "Bitcoin"),
  ETH("Ethereum", 8, "", "Ethereum"),
  XRP("Ripple", 6, "", "XRP");

  public static Currency valueOfName(String name) {
    for (Currency currency : values()) {
      if (currency.name().equals(name)) {
        return currency;
      }
    }

    return null;
  }

  @Getter private final String label;

  @Getter private final int precision;

  @Getter private final String chain;

  @Getter private final String network;

  Currency(String label, int precision, String chain, String network) {
    this.label = label;
    this.precision = precision;
    this.chain = chain;
    this.network = network;
  }

  public String getName() {
    return name();
  }

  public String toLowerCase() {
    return name().toLowerCase();
  }

  public String toCamelCase() {
    return name().charAt(0) + name().toLowerCase().substring(1);
  }

  public boolean isJpy() {
    return this == JPY;
  }

  public BigDecimal getScaledAmount(BigDecimal amount, RoundingMode roundingMode) {
    // 返回一个指定精度的BigDecimal对象，使用指定的舍入模式
    return amount.setScale(precision, roundingMode);
  }

  public BigDecimal getScaledAmount(BigDecimal amount) {
    return getScaledAmount(amount, RoundingMode.HALF_UP);
  }
}
