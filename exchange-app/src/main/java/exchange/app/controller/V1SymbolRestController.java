package exchange.app.controller;

import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import exchange.common.config.IconConfig;
import exchange.common.constant.TradeType;
import exchange.common.controller.AbstractRestController;
import exchange.common.entity.Symbol;
import exchange.common.model.response.SymbolData;
import exchange.common.service.CurrencyPairConfigService;
import exchange.common.service.SymbolService;
import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@RestController
@RequestMapping("/app/v1/symbol")
@Timed
public class V1SymbolRestController extends AbstractRestController {

  private final CurrencyPairConfigService currencyPairConfigService;

  private final SymbolService symbolService;

  private final IconConfig iconConfig;

  @GetMapping
  public ResponseEntity<List<SymbolData>> get(HttpServletResponse response) {
    setCacheControlForPublic(response);
    List<SymbolData> symbolDatas = new ArrayList<>();

    // 有効(enabled=true)な通貨ペアのリスト
    currencyPairConfigService
        .findAllByCondition(TradeType.SPOT, null, true)
        .forEach(
            currencyPairConfig -> {
              Symbol symbol =
                  symbolService.findByCondition(
                      TradeType.SPOT, currencyPairConfig.getCurrencyPair());

              if (symbol != null) {
                String baseIconUrl = iconConfig
                    .getIconUrl(currencyPairConfig.getCurrencyPair().getBaseCurrency().getName());
                String quoteIconUrl = iconConfig
                    .getIconUrl(currencyPairConfig.getCurrencyPair().getQuoteCurrency().getName());
                symbolDatas
                    .add(new SymbolData(symbol, currencyPairConfig, baseIconUrl, quoteIconUrl));
              }
            });

    return ResponseEntity.ok(symbolDatas);
  }
}

