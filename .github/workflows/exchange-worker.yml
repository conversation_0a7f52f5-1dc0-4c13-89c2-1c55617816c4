name: On push for exchange-worker

on:
  push:
    branches: [dev, dev2, dev3, stg, prd]
    paths:
      - "exchange-common/**"
      - "exchange-worker/**"
      - ".github/workflows/exchange-worker.yml"
      - ".github/workflows/internal_cicd.yml"
      - "scripts/**"

jobs:
  call-workflow:
    uses: coinbook/cb-exchange-server/.github/workflows/internal_cicd.yml@main
    with:
      project: exchange-worker
      target: ${{ github.ref_name }}
    secrets:
      AWS_ACCESS_KEY_ID: ${{ secrets.CICD_AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.CICD_AWS_SECRET_ACCESS_KEY }}
