package exchange.mmh.worker;

import exchange.common.constant.CandlestickType;
import exchange.common.entity.Symbol;
import exchange.pos.service.PosCandlestickService;
import exchange.mmh.component.Worker;
import java.util.Date;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class PosCandlestickMaker extends Worker {

  private final PosCandlestickService posCandlestickService;

  @Override
  public void execute(Symbol symbol, Map<String, Object> params) {
    // 获取当前时间
    Date date = new Date();

    // 遍历所有的蜡烛图类型
    for (CandlestickType candlestickType : CandlestickType.values()) {
      // 调用posCandlestickService的make方法，生成蜡烛图
      posCandlestickService.make(symbol, candlestickType, date);
    }
  }
}
