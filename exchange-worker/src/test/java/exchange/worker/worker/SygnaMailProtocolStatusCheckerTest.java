package exchange.worker.worker;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

/**
 * @author: wen.y
 * @date: 2024/8/15
 */
@Slf4j
@SpringBootTest(classes = exchange.worker.Application.class)
@ActiveProfiles({"local"})
public class SygnaMailProtocolStatusCheckerTest {

	@Resource
	private SygnaMailProtocolStatusChecker sygnaMailProtocolStatusChecker;

	@Test
	public void executeTest() throws Exception {
		sygnaMailProtocolStatusChecker.execute(null, null);
	}
}
