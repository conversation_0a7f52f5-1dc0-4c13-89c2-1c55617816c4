package exchange.worker.worker;

import exchange.worker.Application;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: wen.y
 * @date: 2024/12/14
 */
@Slf4j
@SpringBootTest(classes = Application.class)
@ActiveProfiles("local")
public class DowJonesSamCaseUpdaterTest {

	@Resource
	private DowJonesSamCaseUpdater dowJonesSamCaseUpdater;

	@Test
	public void execute() throws Exception {
		Map<String, Object> params = new HashMap<>();
		params.put("userType", "CORPORATE");
//		params.put("userType", "PERSONAL");
		dowJonesSamCaseUpdater.execute(null, params);
	}

	public void allUpdate() throws Exception {
		Map<String, Object> params = new HashMap<>();
		params.put("taskType", "all_update");
//		params.put("userType", "CORPORATE");
		params.put("userType", "PERSONAL");
		dowJonesSamCaseUpdater.execute(null, params);
	}

}
