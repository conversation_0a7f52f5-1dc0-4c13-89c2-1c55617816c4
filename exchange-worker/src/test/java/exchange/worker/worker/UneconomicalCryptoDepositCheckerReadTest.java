package exchange.worker.worker;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.SpyBean;
import exchange.common.constant.Currency;
import exchange.common.constant.DepositStatus;
import exchange.common.service.DepositService;
import exchange.common.service.UneconomicalCryptoDepositUserService;
import exchange.spot.entity.SpotCandlestickAdaJpy;
import exchange.spot.service.SpotCandlestickAdaJpyService;

public class UneconomicalCryptoDepositCheckerReadTest extends BaseReaderTest {

  @Autowired
  UneconomicalCryptoDepositUserService service;

  @SpyBean
  SpotCandlestickAdaJpyService spotCandlestickAdaJpyService;

  @SpyBean
  DepositService depositService;

  @BeforeEach
  public void beforeEach() {
    // 入金テーブルをリセットする
    executeSql(masterEM, "DELETE FROM deposit");
  }

  @DisplayName("read empty")
  @Test
  void readEmptyTest() { 
    var candlestick = new SpotCandlestickAdaJpy();
    candlestick.setClose(new BigDecimal("10"));
    doReturn(candlestick).when(spotCandlestickAdaJpyService).findOneByCondition(any(), any(), any(), any(), any(Boolean.class));

    var today = LocalDate.now();
    List<Currency> currencyList = new  ArrayList<Currency>();
    currencyList.add(Currency.ADA);
    var readData = service.read(currencyList, today);
    assertEquals(readData.date(), today);
    assertEquals(readData.depositsByCurrencies().get(0).currency(), Currency.ADA);
    assertEquals(readData.depositsByCurrencies().get(0).jpyPrice(), new BigDecimal("10"));
    assertEquals(readData.depositsByCurrencies().get(0).deposits().size(), 0);
  }

  @DisplayName("read boundary of a date")
  @Test
  void readBoundaryTest() { 
    var today = LocalDate.now();
    // date to string
    var todayStr = today.atStartOfDay(ZoneId.of("Asia/Tokyo")).minusNanos(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    var tomorrow = today.atStartOfDay(ZoneId.of("Asia/Tokyo")).plusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    insertDeposit(masterEM, 1L, Currency.ADA, DepositStatus.DONE, todayStr); 
    insertDeposit(masterEM, 2L, Currency.ADA, DepositStatus.DONE, tomorrow); 

    var candlestick = new SpotCandlestickAdaJpy();
    candlestick.setClose(new BigDecimal("100000001"));
    doReturn(candlestick).when(spotCandlestickAdaJpyService).findOneByCondition(any(), any(), any(), any(), any(Boolean.class));

    List<Currency> currencyList = new  ArrayList<Currency>();
    currencyList.add(Currency.ADA);
    var readData = service.read(currencyList, today);
    assertEquals(readData.date(), today);
    assertEquals(readData.depositsByCurrencies().get(0).currency(), Currency.ADA);
    assertEquals(readData.depositsByCurrencies().get(0).jpyPrice(), new BigDecimal("100000001"));
    assertEquals(readData.depositsByCurrencies().get(0).deposits().size(), 0);
  }

  @DisplayName("read hit")
  @Test
  void readHitTest() { 
    var today = LocalDate.now();
    // date to string
    var todayStr = today.atStartOfDay(ZoneId.of("Asia/Tokyo")).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    insertDeposit(masterEM, 1L, Currency.ADA, DepositStatus.DONE, todayStr); 

    var candlestick = new SpotCandlestickAdaJpy();
    candlestick.setClose(new BigDecimal("100000001"));
    doReturn(candlestick).when(spotCandlestickAdaJpyService).findOneByCondition(any(), any(), any(), any(), any(Boolean.class));

    List<Currency> currencyList = new  ArrayList<Currency>();
    currencyList.add(Currency.ADA);
    var readData = service.read(currencyList, today);
    assertEquals(readData.date(), today);
    assertEquals(readData.depositsByCurrencies().get(0).currency(), Currency.ADA);
    assertEquals(readData.depositsByCurrencies().get(0).jpyPrice(), new BigDecimal("100000001"));
    assertEquals(readData.depositsByCurrencies().get(0).deposits().size(), 1);
    assertEquals(readData.depositsByCurrencies().get(0).deposits().get(0).getId(), 1L);
  }


  // ---- utility ----
  protected static BigDecimal big(String value) {
    return new BigDecimal(value);
  }

  // yyyy/MM/dd HH:mm:ss
  protected static Date toDate(String strDate) {
    final var zdt = ZonedDateTime.parse(
        strDate + "+0000",
        DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ssZ"));
    return Date.from(zdt.toInstant());
  }
}
