package exchange.worker.worker;

import static org.assertj.core.api.Assertions.assertThat;

import exchange.common.constant.Currency;
import exchange.common.constant.TmsStatus;
import exchange.common.entity.Deposit;
import exchange.common.entity.FrequentCryptoDepositUser;
import exchange.worker.worker.FrequentCryptoDepositChecker.ReadData;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.assertj.core.groups.Tuple;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@AutoConfigureMockMvc(addFilters = false)
@ActiveProfiles({"test"})
public class FrequentCryptoDepositCheckerProcessTest extends BaseProcessorTest {

  @Autowired
  FrequentCryptoDepositChecker checker;

  @DisplayName("データなしの場合")
  @Test
  void emptyTest() {
    // **************** 初期値を設定する ****************
    final var readData = new ReadData(
        new ArrayList<>(),
        new Date()
    );

    // **************** 処理を実行する ****************
    final var writeDtos = checker.process(readData);

    // **************** 結果を検証する ****************
    assertThat(writeDtos).as("出力データなし").isEmpty();
  }

  @DisplayName("検知の閾値を超えない場合")
  @Test
  void doesNotExceedThresholdTest() {
    // **************** 初期値を設定する ****************
    final var readData = new ReadData(
        List.of(
            generateEntity(1L, 1L, Currency.ADA, "2022/02/01 00:00:00.000", "2022/02/01 00:00:00.000"),
            generateEntity(2L, 1L, Currency.ADA, "2022/02/01 00:00:00.001", "2022/02/01 00:00:00.001")
        ),
        new Date()
    );

    // **************** 処理を実行する ****************
    final var writeDtos = checker.process(readData);

    // **************** 結果を検証する ****************
    assertThat(writeDtos).as("出力データなし").isEmpty();
  }

  @DisplayName("検知の閾値を超える場合")
  @Test
  void exceedThresholdTest() {
    // **************** 初期値を設定する ****************
    final var readData = new ReadData(
        List.of(
            generateEntity(1L, 1L, Currency.ADA, "2022/02/01 00:00:00.000", "2022/02/01 00:00:00.000"),
            generateEntity(2L, 1L, Currency.ADA, "2022/02/01 00:00:00.001", "2022/02/01 00:00:00.001"),
            generateEntity(3L, 1L, Currency.ADA, "2022/02/01 00:00:00.002", "2022/02/01 00:00:00.002")
        ),
        new Date()
    );

    // **************** 処理を実行する ****************
    final var writeDtos = checker.process(readData);

    // **************** 結果を検証する ****************
    assertThat(writeDtos).as("出力データあり").isNotEmpty();
    assertThat(writeDtos)
        .as("targetAt, userId, tmsStatus, depositCount")
        .extracting(
            FrequentCryptoDepositUser::getTargetAt,
            FrequentCryptoDepositUser::getUserId,
            FrequentCryptoDepositUser::getTmsStatus,
            FrequentCryptoDepositUser::getDepositCount
        )
        .containsExactly(
            Tuple.tuple(readData.dateFrom(), 1L, TmsStatus.OPEN, 3)
        );
  }

  @DisplayName("日付境界テスト 検知の閾値を超えない場合1")
  @Test
  void dateBoundaryNotExceedThresholdTest1() {
    // **************** 初期値を設定する ****************
    final var readData = new ReadData(
        List.of(
            generateEntity(1L, 1L, Currency.ADA, "2022/02/01 00:00:00.000", "2022/02/01 00:00:00.000"),
            generateEntity(2L, 1L, Currency.ADA, "2022/02/01 01:00:00.000", "2022/02/01 01:00:00.000"),
            generateEntity(3L, 1L, Currency.ADA, "2022/02/01 01:00:00.001", "2022/02/01 01:00:00.001")
        ),
        new Date()
    );

    // **************** 処理を実行する ****************
    final var writeDtos = checker.process(readData);

    // **************** 結果を検証する ****************
    assertThat(writeDtos).as("出力データなし").isEmpty();
  }

  @DisplayName("日付境界テスト 過去データ検知しない")
  @Test
  void dateBoundaryNotExceedThresholdTest2() {
    // **************** 初期値を設定する ****************
    final var readData = new ReadData(
        List.of(
            generateEntity(5L, 1L, Currency.ADA, "2025/02/01 00:00:00.000", "2025/02/01 00:00:00.000"),
            generateEntity(4L, 1L, Currency.ADA, "2024/02/01 00:00:00.000", "2024/02/01 00:00:00.000"),
            generateEntity(3L, 1L, Currency.ADA, "2023/02/01 00:00:00.000", "2023/02/01 00:00:00.000"),
            generateEntity(2L, 1L, Currency.ADA, "2022/02/01 00:00:00.000", "2022/02/01 00:00:00.000"),
            generateEntity(1L, 1L, Currency.ADA, "2021/02/01 00:00:00.000", "2021/02/01 00:00:00.000")
        ),
        new Date()
    );

    // **************** 処理を実行する ****************
    final var writeDtos = checker.process(readData);

    // **************** 結果を検証する ****************
    assertThat(writeDtos).as("出力データなし").isEmpty();
  }

  @DisplayName("日付境界テスト 検知の閾値を超える場合1")
  @Test
  void dateBoundaryExceedThresholdTest1() {
    // **************** 初期値を設定する ****************
    final var readData = new ReadData(
        List.of(
            generateEntity(1L, 1L, Currency.ADA, "2022/02/01 00:00:00.000", "2022/02/01 00:00:00.000"),
            generateEntity(2L, 1L, Currency.ADA, "2022/02/01 00:59:59.999", "2022/02/01 00:59:59.999"),
            generateEntity(3L, 1L, Currency.ADA, "2022/02/01 01:00:00.000", "2022/02/01 01:00:00.000")
        ),
        new Date()
    );

    // **************** 処理を実行する ****************
    final var writeDtos = checker.process(readData);

    // **************** 結果を検証する ****************
    assertThat(writeDtos).as("出力データあり").isNotEmpty();
    assertThat(writeDtos)
        .as("targetAt, userId, tmsStatus, depositCount")
        .extracting(
            FrequentCryptoDepositUser::getTargetAt,
            FrequentCryptoDepositUser::getUserId,
            FrequentCryptoDepositUser::getTmsStatus,
            FrequentCryptoDepositUser::getDepositCount
        )
        .containsExactly(
            Tuple.tuple(readData.dateFrom(), 1L, TmsStatus.OPEN, 3)
        );
  }

  @DisplayName("日付境界テスト 検知の閾値を超える場合2")
  @Test
  void dateBoundaryExceedThresholdTest2() {
    // **************** 初期値を設定する ****************
    final var readData = new ReadData(
        List.of(
            generateEntity(1L, 1L, Currency.ADA, "2022/02/01 00:00:00.000", "2022/02/01 00:00:00.000"),
            generateEntity(2L, 1L, Currency.ADA, "2022/02/01 01:00:00.000", "2022/02/01 01:00:00.000"),
            generateEntity(3L, 1L, Currency.ADA, "2022/02/01 01:00:00.001", "2022/02/01 01:00:00.001"),
            generateEntity(4L, 1L, Currency.ADA, "2022/02/01 01:00:00.002", "2022/02/01 01:00:00.002")
        ),
        new Date()
    );

    // **************** 処理を実行する ****************
    final var writeDtos = checker.process(readData);

    // **************** 結果を検証する ****************
    assertThat(writeDtos).as("出力データあり").isNotEmpty();
    assertThat(writeDtos)
        .as("targetAt, userId, tmsStatus, depositCount")
        .extracting(
            FrequentCryptoDepositUser::getTargetAt,
            FrequentCryptoDepositUser::getUserId,
            FrequentCryptoDepositUser::getTmsStatus,
            FrequentCryptoDepositUser::getDepositCount
        )
        .containsExactly(
            Tuple.tuple(readData.dateFrom(), 1L, TmsStatus.OPEN, 4)
        );
  }

  @DisplayName("日付境界テスト データID降順 検知の閾値を超える場合3")
  @Test
  void dateBoundaryExceedThresholdTest3() {
    // **************** 初期値を設定する ****************
    final var readData = new ReadData(
        List.of(
            generateEntity(4L, 1L, Currency.ADA, "2022/02/01 01:00:00.002", "2022/02/01 01:00:00.002"),
            generateEntity(3L, 1L, Currency.ADA, "2022/02/01 01:00:00.001", "2022/02/01 01:00:00.001"),
            generateEntity(2L, 1L, Currency.ADA, "2022/02/01 01:00:00.000", "2022/02/01 01:00:00.000"),
            generateEntity(1L, 1L, Currency.ADA, "2022/02/01 00:00:00.000", "2022/02/01 00:00:00.000")
        ),
        new Date()
    );

    // **************** 処理を実行する ****************
    final var writeDtos = checker.process(readData);

    // **************** 結果を検証する ****************
    assertThat(writeDtos).as("出力データあり").isNotEmpty();
    assertThat(writeDtos)
        .as("targetAt, userId, tmsStatus, depositCount")
        .extracting(
            FrequentCryptoDepositUser::getTargetAt,
            FrequentCryptoDepositUser::getUserId,
            FrequentCryptoDepositUser::getTmsStatus,
            FrequentCryptoDepositUser::getDepositCount
        )
        .containsExactly(
            Tuple.tuple(readData.dateFrom(), 1L, TmsStatus.OPEN, 4)
        );
  }

  @DisplayName("通貨を跨いでも正しく検知するかテスト")
  @Test
  void crossCurrencyTest() {
    // **************** 初期値を設定する ****************
    final var readData = new ReadData(
        List.of(
            generateEntity(1L, 1L, Currency.ADA, "2022/02/01 00:00:00.000", "2022/02/01 00:00:00.000"),
            generateEntity(2L, 1L, Currency.JPY, "2022/02/01 00:00:00.000", "2022/02/01 00:00:00.000"),
            generateEntity(3L, 1L, Currency.NIDT, "2022/02/01 00:00:00.000", "2022/02/01 00:00:00.000")
        ),
        new Date()
    );

    // **************** 処理を実行する ****************
    final var writeDtos = checker.process(readData);

    // **************** 結果を検証する ****************
    assertThat(writeDtos).as("出力データあり").isNotEmpty();
    assertThat(writeDtos)
        .as("targetAt, userId, tmsStatus, depositCount")
        .extracting(
            FrequentCryptoDepositUser::getTargetAt,
            FrequentCryptoDepositUser::getUserId,
            FrequentCryptoDepositUser::getTmsStatus,
            FrequentCryptoDepositUser::getDepositCount
        )
        .containsExactly(
            Tuple.tuple(readData.dateFrom(), 1L, TmsStatus.OPEN, 3)
        );
  }

  @DisplayName("検知対象が複数のユーザーの場合")
  @Test
  void detectionMultipleUsersTest() {
    // **************** 初期値を設定する ****************
    final var readData = new ReadData(
        List.of(
            generateEntity(1L, 1L, Currency.ADA, "2022/02/01 00:00:00.000", "2022/02/01 00:00:00.000"),
            generateEntity(2L, 1L, Currency.ADA, "2022/02/01 00:00:00.000", "2022/02/01 00:00:00.000"),
            generateEntity(3L, 2L, Currency.ADA, "2022/02/01 00:00:00.000", "2022/02/01 00:00:00.000"),
            generateEntity(4L, 2L, Currency.ADA, "2022/02/01 00:00:00.000", "2022/02/01 00:00:00.000"),
            generateEntity(5L, 2L, Currency.ADA, "2022/02/01 00:00:00.000", "2022/02/01 00:00:00.000"),
            generateEntity(6L, 3L, Currency.ADA, "2022/02/01 00:00:00.000", "2022/02/01 00:00:00.000"),
            generateEntity(7L, 4L, Currency.ADA, "2022/02/01 00:00:00.000", "2022/02/01 00:00:00.000"),
            generateEntity(8L, 4L, Currency.ADA, "2022/02/01 00:00:00.000", "2022/02/01 00:00:00.000"),
            generateEntity(9L, 4L, Currency.ADA, "2022/02/01 00:00:00.000", "2022/02/01 00:00:00.000"),
            generateEntity(10L, 4L, Currency.ADA, "2022/02/01 00:00:00.000", "2022/02/01 00:00:00.000")
        ),
        new Date()
    );

    // **************** 処理を実行する ****************
    final var writeDtos = checker.process(readData);

    // **************** 結果を検証する ****************
    assertThat(writeDtos).as("出力データあり").isNotEmpty();
    assertThat(writeDtos)
        .as("targetAt, userId, tmsStatus, depositCount")
        .extracting(
            FrequentCryptoDepositUser::getTargetAt,
            FrequentCryptoDepositUser::getUserId,
            FrequentCryptoDepositUser::getTmsStatus,
            FrequentCryptoDepositUser::getDepositCount
        )
        .containsExactly(
            Tuple.tuple(readData.dateFrom(), 2L, TmsStatus.OPEN, 3),
            Tuple.tuple(readData.dateFrom(), 4L, TmsStatus.OPEN, 4)
        );
  }

  @DisplayName("日付テスト 出金データはupdatedAtを使用するので確認")
  @Test
  void checkUpdatedAtTest1() {
    // **************** 初期値を設定する ****************
    final var readData = new ReadData(
        List.of(
            generateEntity(1L, 1L, Currency.ADA, "2022/02/01 00:00:00.000", "2022/02/01 00:00:00.000"),
            generateEntity(2L, 1L, Currency.ADA, "2022/02/01 00:00:00.001", "2022/02/01 01:00:00.001"),
            generateEntity(3L, 1L, Currency.ADA, "2022/02/01 00:00:00.002", "2022/02/01 01:00:00.002"),
            generateEntity(4L, 2L, Currency.ADA, "2022/02/01 00:00:00.000", "2022/02/01 00:00:00.000"),
            generateEntity(5L, 2L, Currency.ADA, "2022/02/01 01:00:00.001", "2022/02/01 00:00:00.001"),
            generateEntity(6L, 2L, Currency.ADA, "2022/02/01 03:00:00.002", "2022/02/01 00:00:00.002")
        ),
        new Date()
    );

    // **************** 処理を実行する ****************
    final var writeDtos = checker.process(readData);

    // **************** 結果を検証する ****************
    assertThat(writeDtos).as("出力データあり").isNotEmpty();
    assertThat(writeDtos)
        .as("targetAt, userId, tmsStatus, depositCount")
        .extracting(
            FrequentCryptoDepositUser::getTargetAt,
            FrequentCryptoDepositUser::getUserId,
            FrequentCryptoDepositUser::getTmsStatus,
            FrequentCryptoDepositUser::getDepositCount
        )
        .containsExactly(
            Tuple.tuple(readData.dateFrom(), 2L, TmsStatus.OPEN, 3)
        );
  }

  // utils
  private Deposit generateEntity(
      final Long id,
      final Long userId,
      final Currency currency,
      final String createdAt,
      final String updatedAt) {
    final var entity = new Deposit();

    entity.setId(id);
    entity.setUserId(userId);
    entity.setCurrency(currency);
    entity.setCreatedAt(toDateSSS(createdAt));
    entity.setUpdatedAt(toDateSSS(updatedAt));

    return entity;
  }
}
