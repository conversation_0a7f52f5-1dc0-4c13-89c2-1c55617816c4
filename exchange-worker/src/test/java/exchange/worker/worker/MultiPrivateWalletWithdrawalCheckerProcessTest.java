package exchange.worker.worker;

import static org.assertj.core.api.Assertions.assertThat;


import exchange.common.constant.TmsStatus;
import exchange.common.entity.MultiPrivateWalletWithdrawalUser;
import exchange.common.entity.WithdrawalAccount;
import exchange.common.service.MultiPrivateWalletWithdrawalUserService;
import exchange.worker.worker.MultiPrivateWalletWithdrawalChecker.ReadData;
import java.util.ArrayList;
import java.util.List;
import org.assertj.core.groups.Tuple;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@AutoConfigureMockMvc(addFilters = false)
@ActiveProfiles({"test"})
public class MultiPrivateWalletWithdrawalCheckerProcessTest extends BaseProcessorTest {

  @Autowired
  MultiPrivateWalletWithdrawalChecker checker;

  @Autowired
  MultiPrivateWalletWithdrawalUserService multiPrivateWalletWithdrawalUserService;


  @DisplayName("対象の出金アカウントが0")
  @Test
  void noMatchedWithdrawalAccountTest() {
    // **************** 初期値を設定する ****************
    // 初期データ なし
    final var readData = new ReadData(
        new ArrayList<>()
        );

    // **************** 処理を実行する ****************
    final var writeDto = checker.process(readData, toDateSSS("2022/01/01 00:00:00.000"));

    // **************** 結果を検証する ****************
    assertThat(writeDto).as("該当データなし").isEmpty();
  }

  @DisplayName("対象の出金アカウント閾値テスト")
  @Test
  void thhresholdorMoreWithdrawalAccountTest() {
    // **************** 初期値を設定する ****************
    final var readData = new ReadData(
        List.of(
            //user1: 対象アカウント4件検出 -> ◯
            generateEntity(1L,1L,"プライベートウォレット等","2021/12/31 23:59:00.000"),
            generateEntity(2L,1L,"プライベートウォレット等","2021/12/31 23:59:00.000"),
            generateEntity(3L,1L,"プライベートウォレット等","2021/12/31 23:59:00.000"),
            generateEntity(4L,1L,"プライベートウォレット等","2021/12/31 23:59:00.000"),
            //user2: 対象アカウント3件検出 -> ◯
            generateEntity(5L,2L,"プライベートウォレット等","2021/12/31 23:59:00.000"),
            generateEntity(6L,2L,"プライベートウォレット等","2021/12/31 23:59:00.000"),
            generateEntity(6L,2L,"プライベートウォレット等","2021/12/31 23:59:00.000"),
            //user3: 対象アカウント2件検出 -> x
            generateEntity(7L,3L,"プライベートウォレット等","2021/12/31 23:59:00.000"),
            generateEntity(8L,3L,"プライベートウォレット等","2021/12/31 23:59:00.000")
        )
    );

    // **************** 処理を実行する ****************
    final var writeDto = checker.process(readData, toDateSSS("2021/12/31 23:59:00.000"));

    // **************** 結果を検証する ****************
    assertThat(writeDto)
        .as("userId, tmsStatus")
        .extracting(
            MultiPrivateWalletWithdrawalUser::getUserId,
            MultiPrivateWalletWithdrawalUser::getTmsStatus
        )
        .containsExactly(
            Tuple.tuple(1L, TmsStatus.OPEN),
            Tuple.tuple(2L, TmsStatus.OPEN)
        );
  }

  // 以下Utils関数
  private WithdrawalAccount generateEntity(final Long id, final Long userId, final String addressType,
      final String createdAt) {
    final var entity = new WithdrawalAccount();

    entity.setId(id);
    entity.setUserId(userId);
    entity.setAddresstype(addressType);
    entity.setCreatedAt(toDateSSS(createdAt));

    return entity;
  }
};

