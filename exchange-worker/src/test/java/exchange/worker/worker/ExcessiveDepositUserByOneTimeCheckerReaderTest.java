package exchange.worker.worker;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.SpyBean;
import exchange.common.constant.Currency;
import exchange.common.constant.DepositStatus;
import exchange.common.service.DepositService;
import exchange.common.service.ExcessiveDepositUserByOneTimeService;
import exchange.spot.entity.SpotCandlestickAdaJpy;
import exchange.spot.service.SpotCandlestickAdaJpyService;

public class ExcessiveDepositUserByOneTimeCheckerReaderTest extends BaseReaderTest {

  @Autowired
  ExcessiveDepositUserByOneTimeService service;

  @SpyBean
  SpotCandlestickAdaJpyService spotCandlestickAdaJpyService;

  @SpyBean
  DepositService depositService;

  @BeforeEach
  public void beforeEach() {
    // 入金テーブルをリセットする
    executeSql(masterEM, "DELETE FROM deposit");
  }

  @DisplayName("read empty - 0 record")
  @Test
  void readEmptyTest() { 
    var candlestick = new SpotCandlestickAdaJpy();
    candlestick.setClose(new BigDecimal("10"));
    doReturn(candlestick).when(spotCandlestickAdaJpyService).findOneByCondition(any(), any(), any(), any(), any(Boolean.class));

    var today = LocalDate.now();
    var readData = service.read(Currency.ADA, today);
    assertEquals(readData.date(), today);
    assertEquals(readData.currency(), Currency.ADA);
    assertEquals(readData.jpyPrice(), new BigDecimal("10"));
    assertEquals(readData.deposits().size(), 0);
  }

  @DisplayName("read empty - other currency JPY")
  @Test
  void readEmptyOnlyJPYTest() { 
    var candlestick = new SpotCandlestickAdaJpy();
    candlestick.setClose(new BigDecimal("10"));
    doReturn(candlestick).when(spotCandlestickAdaJpyService).findOneByCondition(any(), any(), any(), any(), any(Boolean.class));


    var today = LocalDate.now();
    var todayStr = today.atStartOfDay(ZoneId.of("Asia/Tokyo")).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    insertDeposit(masterEM, 1L, Currency.JPY, DepositStatus.DONE, todayStr); 

    var readData = service.read(Currency.ADA, today);
    assertEquals(readData.date(), today);
    assertEquals(readData.currency(), Currency.ADA);
    assertEquals(readData.jpyPrice(), new BigDecimal("10"));
    assertEquals(readData.deposits().size(), 0);
  }

  @DisplayName("read empty - other Status")
  @Test
  void readEmptyOtherStatus() { 
    var candlestick = new SpotCandlestickAdaJpy();
    candlestick.setClose(new BigDecimal("10"));
    doReturn(candlestick).when(spotCandlestickAdaJpyService).findOneByCondition(any(), any(), any(), any(), any(Boolean.class));


    var today = LocalDate.now();
    var todayStr = today.atStartOfDay(ZoneId.of("Asia/Tokyo")).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

    insertDeposit(masterEM, 1L, Currency.ADA, DepositStatus.REJECTED, todayStr); 
    insertDeposit(masterEM, 2L, Currency.ADA, DepositStatus.REJECTED_FUNDED, todayStr);
    insertDeposit(masterEM, 3L, Currency.ADA, DepositStatus.INABILITY, todayStr);
    insertDeposit(masterEM, 4L, Currency.ADA, DepositStatus.ADD_INFO_CHECKING, todayStr);
    insertDeposit(masterEM, 5L, Currency.ADA, DepositStatus.WAITING_ADD_INFO, todayStr);
    insertDeposit(masterEM, 6L, Currency.ADA, DepositStatus.SECOND_AML_EXAMING, todayStr);
    insertDeposit(masterEM, 7L, Currency.ADA, DepositStatus.WAITING_SECOND_AML_EXAM, todayStr);
    insertDeposit(masterEM, 8L, Currency.ADA, DepositStatus.FIRST_AML_EXAMING, todayStr);
    insertDeposit(masterEM, 9L, Currency.ADA, DepositStatus.AML_INFO_RECIVED, todayStr);
    insertDeposit(masterEM, 10L, Currency.ADA, DepositStatus.SENDER_INFO_RECIVED, todayStr);

    var readData = service.read(Currency.ADA, today);
    assertEquals(readData.date(), today);
    assertEquals(readData.currency(), Currency.ADA);
    assertEquals(readData.jpyPrice(), new BigDecimal("10"));
    assertEquals(readData.deposits().size(), 0);
  }

  @DisplayName("read empty - boundary values of a date")
  @Test
  void readEmptyBoundaryDates() { 
    var candlestick = new SpotCandlestickAdaJpy();
    candlestick.setClose(new BigDecimal("10"));
    doReturn(candlestick).when(spotCandlestickAdaJpyService).findOneByCondition(any(), any(), any(), any(), any(Boolean.class));


    var today = LocalDate.now();
    var todayStr = today.atStartOfDay(ZoneId.of("Asia/Tokyo")).minusNanos(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    var tomorrow = today.atStartOfDay(ZoneId.of("Asia/Tokyo")).plusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    insertDeposit(masterEM, 1L, Currency.ADA, DepositStatus.DONE, todayStr); 
    insertDeposit(masterEM, 2L, Currency.ADA, DepositStatus.DONE, tomorrow); 

    var readData = service.read(Currency.ADA, today);
    assertEquals(readData.date(), today);
    assertEquals(readData.currency(), Currency.ADA);
    assertEquals(readData.jpyPrice(), new BigDecimal("10"));
    assertEquals(readData.deposits().size(), 0);
  }

  @DisplayName("read hit")
  @Test
  void readHitTest() { 
    var today = LocalDate.now();
    // date to string
    var todayStart = today.atStartOfDay(ZoneId.of("Asia/Tokyo")).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    insertDeposit(masterEM, 1L, Currency.ADA, DepositStatus.DONE, todayStart);
    // today end 
    var todayEnd = today.atStartOfDay(ZoneId.of("Asia/Tokyo")).plusDays(1).minusNanos(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    insertDeposit(masterEM, 2L, Currency.ADA, DepositStatus.DONE, todayEnd);

    var candlestick = new SpotCandlestickAdaJpy();
    candlestick.setClose(new BigDecimal("100000001"));
    doReturn(candlestick).when(spotCandlestickAdaJpyService).findOneByCondition(any(), any(), any(), any(), any(Boolean.class));

    
    var readData = service.read(Currency.ADA, today);
    assertEquals(readData.date(), today);
    assertEquals(readData.currency(), Currency.ADA);
    assertEquals(readData.jpyPrice(), new BigDecimal("100000001"));
    assertEquals(readData.deposits().size(), 2);
    assertEquals(readData.deposits().get(0).getCurrency(), Currency.ADA);
    assertEquals(readData.deposits().get(0).getDepositStatus(), DepositStatus.DONE);
  }


  // ---- utility ----
  protected static BigDecimal big(String value) {
    return new BigDecimal(value);
  }

  // yyyy/MM/dd HH:mm:ss
  protected static Date toDate(String strDate) {
    final var zdt = ZonedDateTime.parse(
        strDate + "+0000",
        DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ssZ"));
    return Date.from(zdt.toInstant());
  }
}
