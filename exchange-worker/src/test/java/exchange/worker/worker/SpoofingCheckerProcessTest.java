package exchange.worker.worker;

import static org.assertj.core.api.Assertions.assertThat;

import exchange.common.constant.CancelReason;
import exchange.common.constant.CurrencyPair;
import exchange.common.constant.OrderStatus;
import exchange.common.entity.SpoofingUser;
import exchange.spot.entity.SpotOrder;
import exchange.worker.worker.SpoofingChecker.ReadData;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.assertj.core.groups.Tuple;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@AutoConfigureMockMvc(addFilters = false)
@ActiveProfiles({"test"})
public class SpoofingCheckerProcessTest extends BaseProcessorTest {

  @Autowired
  private SpoofingChecker checker;

  @DisplayName("データなしの場合")
  @Test
  void emptyTest() {
    // **************** 初期値を設定する ****************
    final var readData = new ReadData(
        new ArrayList<>(),
        new ArrayList<>(),
        12,
        new BigDecimal("94.00000000000000000000")
    );

    // **************** 処理を実行する ****************
    final var writeDtos = checker.process(readData);

    // **************** 結果を検証する ****************
    assertThat(writeDtos).as("出力データなし").isEmpty();
  }

  @DisplayName("アクティブオーダーが閾値未満")
  @Test
  void doesNotExceedActiveOrdersThresholdTest() {
    // **************** 初期値を設定する ****************
    final var readData = new ReadData(
        List.of(
            createOrder(1L, 1L, CurrencyPair.ADA_JPY, OrderStatus.WAITING, null),
            createOrder(2L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(3L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(4L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(5L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(6L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(7L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(8L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(9L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(10L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(11L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(12L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(13L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(14L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(15L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(16L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(17L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(18L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(19L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL)
        ),
        new ArrayList<>(),
        12,
        new BigDecimal("94.00000000000000000000")
    );

    // **************** 処理を実行する ****************
    final var writeDtos = checker.process(readData);

    // **************** 結果を検証する ****************
    assertThat(writeDtos).as("出力データなし").isEmpty();
  }

  @DisplayName("ヒストリカルオーダーが閾値未満")
  @Test
  void doesNotExceedHistoryOrdersThresholdTest() {
    // **************** 初期値を設定する ****************
    final var readData = new ReadData(
        new ArrayList<>(),
        List.of(
            createOrder(1L, 1L, CurrencyPair.ADA_JPY, OrderStatus.WAITING, null),
            createOrder(2L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(3L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(4L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(5L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(6L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(7L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(8L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(9L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(10L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(11L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(12L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(13L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(14L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(15L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(16L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(17L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(18L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(19L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL)
        ),
        12,
        new BigDecimal("94.00000000000000000000")
    );

    // **************** 処理を実行する ****************
    final var writeDtos = checker.process(readData);

    // **************** 結果を検証する ****************
    assertThat(writeDtos).as("出力データなし").isEmpty();
  }

  @DisplayName("アクティブオーダー キャンセル率閾値テスト")
  @Test
  void exceedActiveOrdersThresholdTest() {
    // **************** 初期値を設定する ****************
    final var readData = new ReadData(
        List.of(
            // userId1 -> 94% 検知内
            createOrder(1L, 1L, CurrencyPair.ADA_JPY, OrderStatus.WAITING, null),
            createOrder(2L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(3L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(4L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(5L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(6L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(7L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(8L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(9L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(10L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(11L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(12L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(13L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(14L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(15L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(16L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(17L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(18L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(19L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(20L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            // userId2 -> 75% 検知外
            createOrder(21L, 2L, CurrencyPair.ADA_JPY, OrderStatus.WAITING, null),
            createOrder(22L, 2L, CurrencyPair.ADA_JPY, OrderStatus.WAITING, null),
            createOrder(23L, 2L, CurrencyPair.ADA_JPY, OrderStatus.WAITING, null),
            createOrder(24L, 2L, CurrencyPair.ADA_JPY, OrderStatus.WAITING, null),
            createOrder(25L, 2L, CurrencyPair.ADA_JPY, OrderStatus.WAITING, null),
            createOrder(26L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(27L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(28L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(29L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(30L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(31L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(32L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(33L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(34L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(35L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(36L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(37L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(38L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(39L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(40L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            // userId3 -> 76.2% 検知内
            createOrder(41L, 3L, CurrencyPair.ADA_JPY, OrderStatus.WAITING, null),
            createOrder(42L, 3L, CurrencyPair.ADA_JPY, OrderStatus.WAITING, null),
            createOrder(43L, 3L, CurrencyPair.ADA_JPY, OrderStatus.WAITING, null),
            createOrder(44L, 3L, CurrencyPair.ADA_JPY, OrderStatus.WAITING, null),
            createOrder(45L, 3L, CurrencyPair.ADA_JPY, OrderStatus.WAITING, null),
            createOrder(46L, 3L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(47L, 3L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(48L, 3L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(49L, 3L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(50L, 3L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(51L, 3L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(52L, 3L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(53L, 3L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(54L, 3L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(55L, 3L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(56L, 3L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(57L, 3L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(58L, 3L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(59L, 3L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(60L, 3L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(61L, 3L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL)
        ),
        new ArrayList<>(),
        12,
        new BigDecimal("75.00000000000000000000")
    );

    // **************** 処理を実行する ****************
    final var writeDtos = checker.process(readData);

    // **************** 結果を検証する ****************
    assertThat(writeDtos).as("出力データあり").isNotEmpty();
    assertThat(writeDtos)
        .as("targetId, userId, tmsStatus")
        .extracting(
            SpoofingUser::getUserId,
            SpoofingUser::getCanceledOrders,
            SpoofingUser::getTotalOrders
        )
        .containsExactly(
            Tuple.tuple(1L, 19, 20),
            Tuple.tuple(3L, 16, 21)
        );
  }

  @DisplayName("ヒストリカルオーダー キャンセル率閾値テスト")
  @Test
  void exceedHistoryOrdersThresholdTest() {
    // **************** 初期値を設定する ****************
    final var readData = new ReadData(
        new ArrayList<>(),
        List.of(
            // userId1 -> 94% 検知内
            createOrder(1L, 1L, CurrencyPair.ADA_JPY, OrderStatus.WAITING, null),
            createOrder(2L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(3L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(4L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(5L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(6L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(7L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(8L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(9L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(10L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(11L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(12L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(13L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(14L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(15L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(16L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(17L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(18L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(19L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(20L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            // userId2 -> 75% 検知外
            createOrder(21L, 2L, CurrencyPair.ADA_JPY, OrderStatus.WAITING, null),
            createOrder(22L, 2L, CurrencyPair.ADA_JPY, OrderStatus.WAITING, null),
            createOrder(23L, 2L, CurrencyPair.ADA_JPY, OrderStatus.WAITING, null),
            createOrder(24L, 2L, CurrencyPair.ADA_JPY, OrderStatus.WAITING, null),
            createOrder(25L, 2L, CurrencyPair.ADA_JPY, OrderStatus.WAITING, null),
            createOrder(26L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(27L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(28L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(29L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(30L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(31L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(32L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(33L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(34L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(35L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(36L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(37L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(38L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(39L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(40L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            // userId3 -> 76.2% 検知内
            createOrder(41L, 3L, CurrencyPair.ADA_JPY, OrderStatus.WAITING, null),
            createOrder(42L, 3L, CurrencyPair.ADA_JPY, OrderStatus.WAITING, null),
            createOrder(43L, 3L, CurrencyPair.ADA_JPY, OrderStatus.WAITING, null),
            createOrder(44L, 3L, CurrencyPair.ADA_JPY, OrderStatus.WAITING, null),
            createOrder(45L, 3L, CurrencyPair.ADA_JPY, OrderStatus.WAITING, null),
            createOrder(46L, 3L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(47L, 3L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(48L, 3L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(49L, 3L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(50L, 3L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(51L, 3L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(52L, 3L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(53L, 3L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(54L, 3L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(55L, 3L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(56L, 3L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(57L, 3L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(58L, 3L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(59L, 3L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(60L, 3L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(61L, 3L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL)
        ),
        12,
        new BigDecimal("75.00000000000000000000")
    );

    // **************** 処理を実行する ****************
    final var writeDtos = checker.process(readData);

    // **************** 結果を検証する ****************
    assertThat(writeDtos).as("出力データあり").isNotEmpty();
    assertThat(writeDtos)
        .as("targetId, userId, tmsStatus")
        .extracting(
            SpoofingUser::getUserId,
            SpoofingUser::getCanceledOrders,
            SpoofingUser::getTotalOrders
        )
        .containsExactly(
            Tuple.tuple(1L, 19, 20),
            Tuple.tuple(3L, 16, 21)
        );
  }

  @DisplayName("重複が弾かれるかテスト")
  @Test
  void duplicationOrdersThresholdTest() {
    // **************** 初期値を設定する ****************
    final var readData = new ReadData(
        List.of(
            // userId1 -> 19件 検知外
            createOrder(1L, 1L, CurrencyPair.ADA_JPY, OrderStatus.WAITING, null),
            createOrder(2L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(3L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(4L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(5L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(6L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(7L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(8L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(9L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(10L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(11L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(12L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(13L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(14L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(15L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(16L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(17L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(18L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(19L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            // userId2 -> 1件増えて検知内
            createOrder(21L, 2L, CurrencyPair.ADA_JPY, OrderStatus.WAITING, null),
            createOrder(22L, 2L, CurrencyPair.ADA_JPY, OrderStatus.WAITING, null),
            createOrder(23L, 2L, CurrencyPair.ADA_JPY, OrderStatus.WAITING, null),
            createOrder(24L, 2L, CurrencyPair.ADA_JPY, OrderStatus.WAITING, null),
            createOrder(25L, 2L, CurrencyPair.ADA_JPY, OrderStatus.WAITING, null),
            createOrder(26L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(27L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(28L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(29L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(30L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(31L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(32L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(33L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(34L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(35L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(36L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(37L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(38L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(39L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL)
        ),
        List.of(
            createOrder(19L, 1L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(39L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(40L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL),
            createOrder(41L, 2L, CurrencyPair.ADA_JPY, OrderStatus.CANCELED_UNFILLED, CancelReason.CUSTOMER_CANCEL)
        ),
        12,
        new BigDecimal("75.00000000000000000000")
    );

    // **************** 処理を実行する ****************
    final var writeDtos = checker.process(readData);

    // **************** 結果を検証する ****************
    assertThat(writeDtos).as("出力データあり").isNotEmpty();
    assertThat(writeDtos)
        .as("targetId, userId, tmsStatus")
        .extracting(
            SpoofingUser::getUserId,
            SpoofingUser::getCanceledOrders,
            SpoofingUser::getTotalOrders
        )
        .containsExactly(
            Tuple.tuple(2L, 16, 21)
        );
  }

  // utils
  private SpotOrder createOrder(
      Long id, // 注文ID
      Long userId, // ユーザID
      CurrencyPair currencyPair, // 通貨ペア
      OrderStatus orderStatus, // 注文状況
      CancelReason cancelReason // キャンセル理由
  ) {
    final var order = new SpotOrder() {};
    order.setId(id);
    order.setSymbolId(getSymbolId(currencyPair));
    order.setUserId(userId);
    order.setOrderStatus(orderStatus);
    order.setPrice(BigDecimal.ONE);
    order.setAmount(BigDecimal.ONE);
    order.setRemainingAmount(BigDecimal.ONE);
    order.setCancelReason(cancelReason);
    order.setCreatedAt(new Date());
    order.setUpdatedAt(new Date());
    return order;
  }
}
