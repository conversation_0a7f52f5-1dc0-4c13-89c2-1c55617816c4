package exchange.worker.worker;

import com.ibm.icu.text.Transliterator;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.component.SygnaHubManager;
import exchange.common.config.SpringConfig;
import exchange.common.config.WalletConfig;
import exchange.common.constant.Country;
import exchange.common.constant.Currency;
import exchange.common.entity.CurrencyConfig;
import exchange.common.entity.DepositAccount;
import exchange.common.entity.SygnaCustomer;
import exchange.common.entity.SygnaCustomerCurrency;
import exchange.common.entity.User;
import exchange.common.entity.UserInfo;
import exchange.common.entity.UserInfoCorporate;
import exchange.common.model.response.SygnaHubResponse;
import exchange.common.predicate.SygnaCustomerPredicate;
import exchange.common.service.*;
import exchange.common.sygna.SygnaHubClient.TransactionsRes;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.TreeMap;
import java.util.stream.Collectors;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@Slf4j
public class OldSygnaCustomerService1 extends EntityService<SygnaCustomer, SygnaCustomerPredicate> {

  private final SygnaHubManager sygnaHubManager;
  private final DepositAccountService depositAccountService;
  private final CurrencyConfigService currencyConfigService;
  private final WalletConfig walletConfig;
  private final SygnaCurrencyService sygnaCurrencyService;
  private final SygnaCustomerCurrencyService sygnaCustomerCurrencyService;
  @Autowired
  SpringConfig springConfig;

  private static final int max = 50;

  @Override
  public Class<SygnaCustomer> getEntityClass() {
    return SygnaCustomer.class;
  }

  @Override
  protected void fetch(Root<SygnaCustomer> root) {
    super.fetch(root);
  }

  public SygnaCustomer findOneByUserId(Long userId) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<SygnaCustomer, SygnaCustomer>() {
          @Override
          public SygnaCustomer query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.equalUserId(criteriaBuilder, root, userId));
            return getSingleResult(entityManager, criteriaQuery, root, predicates);
          }
        });
  }

  public SygnaCustomer findOneBySygnaCustomerId(String sygnaCustomerId) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<SygnaCustomer, SygnaCustomer>() {
          @Override
          public SygnaCustomer query() {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(predicate.equalSygnaCustomerId(criteriaBuilder, root, sygnaCustomerId));
            return getSingleResult(entityManager, criteriaQuery, root, predicates);
          }
        });
  }

  public void createCustomers(List<User> userList) throws Exception {
    List<List<User>> resultList = averageAssign(userList, max);
    for(List<User> subList : resultList) {
      List<TreeMap<String, Object>> parameters = new ArrayList<>();
      List<SygnaCustomer> sygnaCustomerList = new ArrayList<>();
      List<CreatedCurrency> createdCurrencies = new ArrayList<>();
      List<User> targetUserList = new ArrayList<>(subList);
      for(User user: subList) {
        try {
          SygnaCustomer sygnaCustomer = new SygnaCustomer();
          TreeMap<String, Object> parameter = new TreeMap<>();
          // assets filed
          List<TreeMap<String, Object>> assetsMap = new ArrayList<>();
          List<DepositAccount> depositAccountList =
              depositAccountService.findByCondition(user.getId(), null, null, null, null, null);

          //　有効な通貨資産取得
          List<CurrencyConfig> currencyConfigs = currencyConfigService.findAllByCondition(null, true);
          List<Currency> currencyMst = new ArrayList<>();
          for (CurrencyConfig currencyConfig : currencyConfigs) {
            currencyMst.add(currencyConfig.getCurrency());
          }
          //　JPYは暗号資産処理対象外
          currencyMst.remove(Currency.JPY);
          for (Currency currency : currencyMst) {
            if(ObjectUtils.isNotEmpty(walletConfig.getWithdrawalFrom(currency.name()))) {
              TreeMap<String, Object> asset = new TreeMap<>();
              asset.put("address", walletConfig.getWithdrawalFrom(currency.name()));
              String currencyId = sygnaCurrencyService.getCurrencyId(currency.name());
              if(ObjectUtils.isEmpty(currencyId)) {
                log.info("取引所がサポートしていない通貨です。Sygmbol:" + currency.name());
                continue;
              }
              asset.put("currency_id", currencyId);
              assetsMap.add(asset);
              CreatedCurrency createdCurrency = new CreatedCurrency();
              createdCurrency.setCreatedCurrency(currency.name());
              createdCurrency.setDepositOrWithdrawl(true);
              createdCurrency.setUserId(user.getId());
              createdCurrencies.add(createdCurrency);
            }
          }

          if(ObjectUtils.isNotEmpty(depositAccountList)) {
            for(DepositAccount depositAccount : depositAccountList) {
              TreeMap<String, Object> asset = new TreeMap<>();

              String currencyId = sygnaCurrencyService.getCurrencyId(depositAccount.getCurrency().name());
              if(ObjectUtils.isEmpty(currencyId)) {
                log.info("取引所がサポートしていない通貨です。Sygmbol:" + depositAccount.getCurrency().name()
                    + "　depositAccountId:" + depositAccount.getId());
                continue;
              }
              asset.put("currency_id", currencyId);

              // XRP
              String address = depositAccount.getAddress();
              int index = address.indexOf("@");
              if(Currency.XRP.equals(depositAccount.getCurrency()) && index > 0) {
                TreeMap<String, Object> extraInfo = new TreeMap<>();
                asset.put("address", address.substring(0, index));
                extraInfo.put("tag", address.substring(index + 1));
                asset.put("extra_info", extraInfo);
              } else {
                asset.put("address", address);
              }
              assetsMap.add(asset);
              CreatedCurrency createdCurrency = new CreatedCurrency();
              createdCurrency.setCreatedCurrency(depositAccount.getCurrency().name());
              createdCurrency.setDepositOrWithdrawl(false);
              createdCurrency.setUserId(user.getId());
              createdCurrencies.add(createdCurrency);
            }
          }
          if(ObjectUtils.isNotEmpty(assetsMap)) {
            parameter.put("assets", assetsMap);
          } else {
            targetUserList.remove(user);
            continue;
          }
          DecimalFormat df = new DecimalFormat("********");
          parameter.put("customer_id", springConfig.getEnvironment() + "_" + df.format(user.getId()));
          // customer情報初期化
          String naturalPersonFirstName = "";
          String naturalPersonLastName = "";
          String naturalPersonPhoneticFirstName = "";
          String naturalPersonPhoneticLastName = "";
          String naturalPersonLocalFirstName = "";
          String naturalPersonLocalLastName = "";
          String legalPersonName = "";
          String legalPersonPhoneticName = "";
          String legalPersonLocalName = "";
          String addressType = "";
          String postCode = "";
          String country = "";
          // customerの種類
          Transliterator transLatin = Transliterator.getInstance("Katakana-Latin; Latin-ASCII");
          Transliterator transHalf = Transliterator.getInstance("Fullwidth-Halfwidth");
          String ownUserRecipientType = user.getAuthorities().get(0).getAuthority();
          if("PERSONAL".equals(ownUserRecipientType)) {
            UserInfo userInfo = user.getUserInfo();
            // name
            if(userInfo.getFirstName().matches("^[\\uff21-\\uff3a|\\uff41-\\uff5a]+$")) {
              naturalPersonFirstName = transHalf.transliterate(userInfo.getFirstName())
                  .replaceAll("[^0-9a-zA-Z .,]", "");
            } else {
              naturalPersonFirstName =  transHalf.transliterate(
                  transLatin.transliterate(userInfo.getFirstKana()))
                    .replaceAll("[^0-9a-zA-Z .,]", "");
            }
            if(userInfo.getLastName().matches("^[\\uff21-\\uff3a|\\uff41-\\uff5a]+$")) {
              naturalPersonLastName = transHalf.transliterate(userInfo.getLastName())
                  .replaceAll("[^0-9a-zA-Z .,]", "");
            } else {
              naturalPersonLastName = transHalf.transliterate(
                  transLatin.transliterate(userInfo.getLastKana()))
                    .replaceAll("[^0-9a-zA-Z .,]", "");
            }
            naturalPersonPhoneticFirstName = userInfo.getFirstKana();
            naturalPersonPhoneticLastName = userInfo.getLastKana();
            naturalPersonLocalFirstName = userInfo.getFirstName();
            naturalPersonLocalLastName = userInfo.getLastName();
            // address
            addressType = "HOME";
            // 郵便番号
            postCode = userInfo.getZipCode();
            // 国籍
            country = Country.valueOfLabel(userInfo.getNationality()).name();
          } else if("CORPORATE".equals(ownUserRecipientType)) {
            UserInfoCorporate userInfoCorporate = user.getUserInfoCorporate();
            // name
            String corporateKana =
                userInfoCorporate.getNameKana()
                .replace("カブシキガイシャ", "Co.,Ltd")
                .replace("ゴウドウガイシャ", "LLC")
                .replace("ホールディングス", "Holdings");
            legalPersonName = transHalf.transliterate(
                transLatin.transliterate(corporateKana)).replaceAll("[^0-9a-zA-Z .,]", "");;
            legalPersonPhoneticName = userInfoCorporate.getNameKana();
            legalPersonLocalName = userInfoCorporate.getName();
            // address
            addressType = "BIZZ";
            // 郵便番号
            postCode = userInfoCorporate.getZipCode();
            // 国籍
            country = "JP";
          }
          // person name
          TreeMap<String, Object> personNameMap = new TreeMap<>();
          // natural person
          if(ObjectUtils.isNotEmpty(naturalPersonLastName)) {
            // name
            TreeMap<String, Object> nameMap = new TreeMap<>();
            nameMap.put("first_name", naturalPersonFirstName);
            nameMap.put("last_name", naturalPersonLastName);
            personNameMap.put("name", nameMap);
            // local name
            TreeMap<String, Object> localNameMap = new TreeMap<>();
            localNameMap.put("first_name", naturalPersonLocalFirstName);
            localNameMap.put("last_name", naturalPersonLocalLastName);
            personNameMap.put("local_name", localNameMap);
            // phonetic name
            TreeMap<String, Object> phoneticNameMap = new TreeMap<>();
            phoneticNameMap.put("first_name", naturalPersonPhoneticFirstName);
            phoneticNameMap.put("last_name", naturalPersonPhoneticLastName);
            personNameMap.put("phonetic_name", phoneticNameMap);
            parameter.put("natural_person_name", personNameMap);
            // customer_type 0:個人
            parameter.put("customer_type", 0);
          } else if (ObjectUtils.isNotEmpty(legalPersonName)) {
            // legal person
            // name
            personNameMap.put("name", legalPersonName);
            // local name
            personNameMap.put("local_name", legalPersonLocalName);
            // phonetic name
            personNameMap.put("phonetic_name",  legalPersonPhoneticName);
            parameter.put("legal_person_name", personNameMap);
            // customer_type 1:法人
            parameter.put("customer_type", 1);
          }
          parameter.put("country", country);
          // address
          TreeMap<String, Object> addressMap = new TreeMap<>();
          addressMap.put("address_type", addressType);
          addressMap.put("country", "JP");
          addressMap.put("post_code", postCode);
          parameter.put("address", addressMap);
          parameter.put("assets_ignore_kyt", true);
          sygnaCustomerList.add(sygnaCustomer);
          parameters.add(parameter);
        } catch(Exception e) {
          targetUserList.remove(user);
          log.error("Sygna Customerの作成でエラーが発生しました。userId:" + user.getId() + e.getMessage());
          continue;
        }
      }

      try {
        log.info("Sygna Customer作成の処理対象件数：" + parameters.size());
        SygnaHubResponse<List<TransactionsRes>> res = sygnaHubManager.createCustomers(parameters);
        List<TransactionsRes> txReslist = res.getData();
        if(ObjectUtils.isNotEmpty(txReslist)) {
          int txCount = 0;
          for(User user: targetUserList) {
            TransactionsRes txRes = txReslist.get(txCount);
            SygnaCustomer sygnaCustomer = new SygnaCustomer();
            if(txRes.isSuccess()) {
              List<CreatedCurrency> userCreatedCurrecies = createdCurrencies.stream().
                  filter((c) -> user.getId().equals(c.getUserId())).collect(Collectors.toList());
              customTransactionManager.execute(
                  entityManager -> {
                    sygnaCustomer.setUserId(user.getId());
                    sygnaCustomer.setSygnaCustomerId(txRes.getId());
                    save(sygnaCustomer);
                    // Sygnaで作成済み通貨を登録
                    for(CreatedCurrency createdCurrency : userCreatedCurrecies) {
                      SygnaCustomerCurrency sygnaCustomerCurrency = new SygnaCustomerCurrency();
                      sygnaCustomerCurrency.setCreatedCurrency(createdCurrency.getCreatedCurrency());
                      sygnaCustomerCurrency.setDepositOrWithdrawl(createdCurrency.isDepositOrWithdrawl());
                      SygnaCustomer createdCustomer = findOneBySygnaCustomerId(sygnaCustomer.getSygnaCustomerId());
                      sygnaCustomerCurrency.setSygnaCustomerTableId(createdCustomer.getId());
                      sygnaCustomerCurrencyService.save(sygnaCustomerCurrency);
                    }
              });
            } else {
              log.error("Sygna Customerの作成でエラーが発生しました。userId:" + user.getId()
                + txRes.getError().getMessage());
            }
            txCount++;
          }
        }
      } catch(Exception e) {
        log.error("Sygna Customerの作成でエラーが発生しました。" + e.getMessage());
        continue;
      }
    }
  }

  public void updateCustomers(List<User> userList) throws Exception {
    List<List<User>> resultList = averageAssign(userList, max);
    for(List<User> subList : resultList) {
      List<TreeMap<String, Object>> parameters = new ArrayList<>();
      List<SygnaCustomer> sygnaCustomerList = new ArrayList<>();
      List<CreatedCurrency> createdCurrencies = new ArrayList<>();
      for(User user: subList) {
        try {
          SygnaCustomer sygnaCustomer = findOneByUserId(user.getId());
          TreeMap<String, Object> parameter = new TreeMap<>();
          parameter.put("id", sygnaCustomer.getSygnaCustomerId());
          parameter.put("assets_ignore_kyt", true);
          if(!checkAddressCreated(sygnaCustomer)) {
            // assets filed 暗号資産アドレス作成に伴い、Sygna customerを更新
            List<TreeMap<String, Object>> assetsMap = new ArrayList<>();
            //　有効な通貨資産取得
            List<CurrencyConfig> currencyConfigs = currencyConfigService.findAllByCondition(null, true);
            List<Currency> currencyMst = new ArrayList<>();
            for (CurrencyConfig currencyConfig : currencyConfigs) {
              currencyMst.add(currencyConfig.getCurrency());
            }
            //　JPYは暗号資産処理対象外
            currencyMst.remove(Currency.JPY);
            for (Currency currency : currencyMst) {
              if(ObjectUtils.isNotEmpty(walletConfig.getWithdrawalFrom(currency.name()))) {
                TreeMap<String, Object> asset = new TreeMap<>();
                asset.put("address", walletConfig.getWithdrawalFrom(currency.name()));
                String currencyId = sygnaCurrencyService.getCurrencyId(currency.name());
                if(ObjectUtils.isEmpty(currencyId)) {
                  log.info("取引所がサポートしていない通貨です。Sygmbol:" + currency.name());
                  continue;
                }
                asset.put("currency_id", currencyId);
                assetsMap.add(asset);
                CreatedCurrency createdCurrency = new CreatedCurrency();
                createdCurrency.setCreatedCurrency(currency.name());
                createdCurrency.setSygnaCustomerTableId(sygnaCustomer.getId());
                createdCurrency.setDepositOrWithdrawl(true);
                createdCurrencies.add(createdCurrency);
              }
            }

            List<DepositAccount> depositAccountList =
                depositAccountService.findByCondition(user.getId(), null, null, null, null, null);
            if(ObjectUtils.isNotEmpty(depositAccountList)) {
              for(DepositAccount depositAccount : depositAccountList) {
                TreeMap<String, Object> asset = new TreeMap<>();
                String currencyId = sygnaCurrencyService.getCurrencyId(depositAccount.getCurrency().name());
                if(ObjectUtils.isEmpty(currencyId)) {
                  log.info("取引所がサポートしていない通貨です。Sygmbol:" + depositAccount.getCurrency().name()
                      + "　depositAccountId:" + depositAccount.getId());
                  continue;
                }
                asset.put("currency_id", currencyId);

                // XRP
                String address = depositAccount.getAddress();
                int index = address.indexOf("@");
                if(Currency.XRP.equals(depositAccount.getCurrency()) && index > 0) {
                  TreeMap<String, Object> extraInfo = new TreeMap<>();
                  asset.put("address", address.substring(0, index));
                  extraInfo.put("tag", address.substring(index + 1));
                  asset.put("extra_info", extraInfo);
                } else {
                  asset.put("address", address);
                }

                assetsMap.add(asset);
                CreatedCurrency createdCurrency = new CreatedCurrency();
                createdCurrency.setCreatedCurrency(depositAccount.getCurrency().name());
                createdCurrency.setSygnaCustomerTableId(sygnaCustomer.getId());
                createdCurrency.setDepositOrWithdrawl(false);
                createdCurrencies.add(createdCurrency);
              }
            }
            if(ObjectUtils.isNotEmpty(assetsMap)) {
              parameter.put("assets", assetsMap);
            }
          }

          if(sygnaCustomer.isUserInfoChangeFlg()) {
            DecimalFormat df = new DecimalFormat("********");
            parameter.put("customer_id", springConfig.getEnvironment() + "_" + df.format(user.getId()));
            // customer情報初期化
            String naturalPersonFirstName = "";
            String naturalPersonLastName = "";
            String naturalPersonPhoneticFirstName = "";
            String naturalPersonPhoneticLastName = "";
            String naturalPersonLocalFirstName = "";
            String naturalPersonLocalLastName = "";
            String legalPersonName = "";
            String legalPersonPhoneticName = "";
            String legalPersonLocalName = "";
            String addressType = "";
            String postCode = "";
            String country = "";
            // customerの種類
            Transliterator transLatin = Transliterator.getInstance("Katakana-Latin; Latin-ASCII");
            Transliterator transHalf = Transliterator.getInstance("Fullwidth-Halfwidth");
            String ownUserRecipientType = user.getAuthorities().get(0).getAuthority();
            if("PERSONAL".equals(ownUserRecipientType)) {
              UserInfo userInfo = user.getUserInfo();
              // name
              if(userInfo.getFirstName().matches("^[\\uff21-\\uff3a|\\uff41-\\uff5a]+$")) {
                naturalPersonFirstName = transHalf.transliterate(userInfo.getFirstName())
                    .replaceAll("[^0-9a-zA-Z .,]", "");
              } else {
                naturalPersonFirstName = transHalf.transliterate(
                    transLatin.transliterate(userInfo.getFirstKana()))
                      .replaceAll("[^0-9a-zA-Z .,]", "");
              }
              if(userInfo.getLastName().matches("^[\\uff21-\\uff3a|\\uff41-\\uff5a]+$")) {
                naturalPersonLastName = transHalf.transliterate(userInfo.getLastName())
                    .replaceAll("[^0-9a-zA-Z .,]", "");
              } else {
                naturalPersonLastName = transHalf.transliterate(
                    transLatin.transliterate(userInfo.getLastKana()))
                      .replaceAll("[^0-9a-zA-Z .,]", "");
              }
              naturalPersonPhoneticFirstName = userInfo.getFirstKana();
              naturalPersonPhoneticLastName = userInfo.getLastKana();
              naturalPersonLocalFirstName = userInfo.getFirstName();
              naturalPersonLocalLastName = userInfo.getLastName();
              // address
              addressType = "HOME";
              // 郵便番号
              postCode = userInfo.getZipCode();
              // 国籍
              country = Country.valueOfLabel(userInfo.getNationality()).name();
            } else if("CORPORATE".equals(ownUserRecipientType)) {
              UserInfoCorporate userInfoCorporate = user.getUserInfoCorporate();
              // name
              String corporateKana =
                  userInfoCorporate.getNameKana()
                  .replace("カブシキガイシャ", "Co.,Ltd")
                  .replace("ゴウドウガイシャ", "LLC")
                  .replace("ホールディングス", "Holdings");
              legalPersonName = transHalf.transliterate(
                  transLatin.transliterate(corporateKana)).replaceAll("[^0-9a-zA-Z .,]", "");
              legalPersonPhoneticName = userInfoCorporate.getNameKana();
              legalPersonLocalName = userInfoCorporate.getName();
              // address
              addressType = "BIZZ";
              // 郵便番号
              postCode = userInfoCorporate.getZipCode();
              // 国籍
              country = "JP";
            }
            // person name
            TreeMap<String, Object> personNameMap = new TreeMap<>();
            // natural person
            if(ObjectUtils.isNotEmpty(naturalPersonLastName)) {
              // name
              TreeMap<String, Object> nameMap = new TreeMap<>();
              nameMap.put("first_name", naturalPersonFirstName);
              nameMap.put("last_name", naturalPersonLastName);
              personNameMap.put("name", nameMap);
              // local name
              TreeMap<String, Object> localNameMap = new TreeMap<>();
              localNameMap.put("first_name", naturalPersonLocalFirstName);
              localNameMap.put("last_name", naturalPersonLocalLastName);
              personNameMap.put("local_name", localNameMap);
              // phonetic name
              TreeMap<String, Object> phoneticNameMap = new TreeMap<>();
              phoneticNameMap.put("first_name", naturalPersonPhoneticFirstName);
              phoneticNameMap.put("last_name", naturalPersonPhoneticLastName);
              personNameMap.put("phonetic_name", phoneticNameMap);
              parameter.put("natural_person_name", personNameMap);
              // customer_type 0:個人
              parameter.put("customer_type", 0);
            } else if (ObjectUtils.isNotEmpty(legalPersonName)) {
              // legal person
              // name
              personNameMap.put("name", legalPersonName);
              // local name
              personNameMap.put("local_name", legalPersonLocalName);
              // phonetic name
              personNameMap.put("phonetic_name",  legalPersonPhoneticName);
              parameter.put("legal_person_name", personNameMap);
              // customer_type 1:法人
              parameter.put("customer_type", 1);
            }
            parameter.put("country", country);
            // address
            TreeMap<String, Object> addressMap = new TreeMap<>();
            addressMap.put("address_type", addressType);
            addressMap.put("country", "JP");
            addressMap.put("post_code", postCode);
            addressMap.put("street_name", "");
            addressMap.put("building_name", "");
            addressMap.put("district_name", "");
            addressMap.put("country_sub_division", "");
            parameter.put("address", addressMap);
          }
          sygnaCustomerList.add(sygnaCustomer);
          parameters.add(parameter);
        } catch(Exception e) {
          log.error("Sygna Customerの更新でエラーが発生しました。userId:" + user.getId() + e.getMessage());
          continue;
        }
      }

      try {
        log.info("Sygna Customer更新の処理対象件数：" + parameters.size());
        SygnaHubResponse<List<TransactionsRes>> res = sygnaHubManager.updateCustomers(parameters);
        List<TransactionsRes> txReslist = res.getData();
        if(ObjectUtils.isNotEmpty(txReslist)) {
          int txCount = 0;
          for(SygnaCustomer sygnaCustomer : sygnaCustomerList) {
            TransactionsRes txRes = txReslist.get(txCount);
            if(txRes.isSuccess()) {
              List<CreatedCurrency> userCreatedCurrecies = createdCurrencies.stream().
                  filter((c) -> sygnaCustomer.getId().equals(c.getSygnaCustomerTableId())).collect(Collectors.toList());
              customTransactionManager.execute(
                  entityManager -> {
                    sygnaCustomer.setUserInfoChangeFlg(false);
                    save(sygnaCustomer);
                    // Sygnaで作成済み通貨を登録
                    Set<SygnaCustomerCurrency> createdCurrencySet = sygnaCustomer.getCreatedCurrency();
                    for(CreatedCurrency createdCurrency : userCreatedCurrecies) {
                      if(sygnaCustomer.getId().equals(createdCurrency.getSygnaCustomerTableId())) {
                        if(ObjectUtils.isEmpty(createdCurrencySet) ||
                            !createdCurrencySet.stream().anyMatch(existCurrency ->
                              existCurrency.getCreatedCurrency().equals(createdCurrency.getCreatedCurrency())
                                && existCurrency.isDepositOrWithdrawl() == createdCurrency.isDepositOrWithdrawl())) {
                          SygnaCustomerCurrency sygnaCustomerCurrency = new SygnaCustomerCurrency();
                          sygnaCustomerCurrency.setCreatedCurrency(createdCurrency.getCreatedCurrency());
                          sygnaCustomerCurrency.setDepositOrWithdrawl(createdCurrency.isDepositOrWithdrawl());
                          sygnaCustomerCurrency.setSygnaCustomerTableId(sygnaCustomer.getId());
                          sygnaCustomerCurrencyService.save(sygnaCustomerCurrency);
                        }
                      }
                    }
                  });
            } else {
              log.error("Sygna Customerの更新でエラーが発生しました。"
                + txRes.getError().getMessage());
            }
            txCount++;
          }
        }
      } catch(Exception e) {
        log.error("Sygna Customerの更新でエラーが発生しました。" + e.getMessage());
        continue;
      }
    }
  }

  public boolean checkAddressCreated(SygnaCustomer sygnaCustomer) {
    if(ObjectUtils.isEmpty(sygnaCustomer.getCreatedCurrency())) {
      return false;
    }
    //　有効な通貨資産取得
    List<CurrencyConfig> currencyConfigs = currencyConfigService.findAllByCondition(null, true);
    List<Currency> currencyMst = new ArrayList<>();
    for (CurrencyConfig currencyConfig : currencyConfigs) {
      currencyMst.add(currencyConfig.getCurrency());
    }
    //　JPYは暗号資産処理対象外
    currencyMst.remove(Currency.JPY);
    for(Currency currency : currencyMst) {
      // 通貨の出金アドレスが連携されない場合
      if(!sygnaCustomer.getCreatedCurrency().stream()
        .anyMatch(createdCurrency ->
          createdCurrency.getCreatedCurrency().equals(currency.name()) && createdCurrency.isDepositOrWithdrawl())) {
        return false;
      }
      // 通貨の入金アドレスが連携されない場合
      if(!sygnaCustomer.getCreatedCurrency().stream()
          .anyMatch(createdCurrency ->
            createdCurrency.getCreatedCurrency().equals(currency.name()) && !createdCurrency.isDepositOrWithdrawl())) {
          return false;
      }
    }

    return true;
  }

  @Getter @Setter
  private class CreatedCurrency {

    private Long sygnaCustomerTableId;

    private Long userId;

    private String createdCurrency;

    private boolean depositOrWithdrawl = false;
  }

  public <T> List<List<T>> averageAssign(List<T> source, int splitItemNum) {
    List<List<T>> result = new ArrayList<List<T>>();

    if (source != null && source.size() > 0 && splitItemNum > 0) {
      if (source.size() <= splitItemNum) {
        result.add(source);
      } else {
        int splitNum = (source.size() % splitItemNum == 0) ?
          (source.size() / splitItemNum) : (source.size() / splitItemNum + 1);
        List<T> value = null;
        for (int i = 0; i < splitNum; i++) {
          if (i < splitNum - 1) {
            value = source.subList(i * splitItemNum, (i + 1) * splitItemNum);
          } else {
            value = source.subList(i * splitItemNum, source.size());
          }
          result.add(value);
        }
      }
    }
    return result;
  }
}
