package exchange.worker.worker;

import static org.assertj.core.api.Assertions.assertThat;

import exchange.common.constant.Currency;
import exchange.common.constant.DepositStatus;
import exchange.common.entity.Deposit;
import exchange.common.entity.FrequentCryptoDepositUser;
import exchange.common.service.FrequentCryptoDepositUserService;
import javax.persistence.EntityManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@AutoConfigureMockMvc(addFilters = false)
@ActiveProfiles({"test"})
public class FrequentCryptoDepositCheckerReadTest extends BaseReaderTest {

  @Autowired
  FrequentCryptoDepositChecker checker;

  @Autowired
  FrequentCryptoDepositUserService frequentCryptoDepositUserService;

  @BeforeEach
  public void beforeEach() {
    // 暗号資産高頻度入庫検知テーブルをリセットする
    executeSql(masterEM, "DELETE FROM frequent_crypto_deposit_user");

    // 入金テーブルをリセットする
    executeSql(masterEM, "DELETE FROM deposit");
  }

  @DisplayName("何も取得されない")
  @Test
  void emptyTest() {
    // **************** 初期値を設定する ****************
    // 初期データなし

    // **************** 処理を実行する ****************
    final var readDto = checker.read(toDate(20220101), toDate(20220102));

    // **************** 結果を検証する ****************
    assertThat(readDto.deposits()).as("入金データなし").isEmpty();
    assertThat(readDto.dateFrom())
        .as("dateFromの値がNullじゃない")
        .isNotNull();
    assertThat(readDto.dateFrom().getTime())
        .as("dateFromの値が想定通りかどうか")
        .isEqualTo(toDate(20220101).getTime());
  }

  @DisplayName("日付境界検索テスト")
  @Test
  void dateBoundaryTest() {
    // **************** 初期値を設定する ****************
    insertDeposit(masterEM, 1L, Currency.ADA, DepositStatus.DONE, "2022-01-01 00:00:00.000", "2021-12-31 23:59:59.999");
    insertDeposit(masterEM, 2L, Currency.ADA, DepositStatus.DONE, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertDeposit(masterEM, 3L, Currency.ADA, DepositStatus.DONE, "2022-01-01 00:00:00.000", "2022-01-01 23:59:59.999");
    insertDeposit(masterEM, 4L, Currency.ADA, DepositStatus.DONE, "2022-01-02 00:00:00.000", "2022-01-02 00:00:00.000");
    insertDeposit(masterEM, 5L, Currency.ADA, DepositStatus.DONE, "2022-01-01 00:00:00.000", "2022-01-02 00:00:00.001");


    // **************** 処理を実行する ****************
    final var readDto = checker.read(toDateLocalUtc(20220101), toDateLocalUtc(20220102));

    // **************** 結果を検証する ****************
    assertThat(readDto.deposits()).as("入金データあり").isNotEmpty();
    assertThat(readDto.deposits()).as("所定の入金データが取得される")
        .extracting(Deposit::getId)
        .containsExactly(3L, 2L);

    assertThat(readDto.dateFrom())
        .as("dateFromの値がNullじゃない")
        .isNotNull();
    assertThat(readDto.dateFrom().getTime())
        .as("dateFromの値が想定通りかどうか")
        .isEqualTo(toDateLocalUtc(20220101).getTime());
  }

  @DisplayName("入金ステータステスト")
  @Test
  void depositStatusTest() {
    // **************** 初期値を設定する ****************
    insertDeposit(masterEM, 1L, Currency.ADA, DepositStatus.DONE, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertDeposit(masterEM, 2L, Currency.ADA, DepositStatus.CONFIRMED, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertDeposit(masterEM, 3L, Currency.ADA, DepositStatus.REJECTED_FUNDED, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");


    // **************** 処理を実行する ****************
    final var readDto = checker.read(toDateLocalUtc(20220101), toDateLocalUtc(20220102));

    // **************** 結果を検証する ****************
    assertThat(readDto.deposits()).as("入金データあり").isNotEmpty();
    assertThat(readDto.deposits()).as("所定の入金データが取得される")
        .extracting(Deposit::getId)
        .containsExactly(1L);

    assertThat(readDto.dateFrom())
        .as("dateFromの値がNullじゃない")
        .isNotNull();
    assertThat(readDto.dateFrom().getTime())
        .as("dateFromの値が想定通りかどうか")
        .isEqualTo(toDateLocalUtc(20220101).getTime());
  }

  @DisplayName("通貨別取得テスト")
  @Test
  void currencyTest() {
    // **************** 初期値を設定する ****************
    insertDeposit(masterEM, 1L, Currency.ADA, DepositStatus.DONE, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertDeposit(masterEM, 2L, Currency.JPY, DepositStatus.DONE, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertDeposit(masterEM, 3L, Currency.NIDT, DepositStatus.DONE, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");


    // **************** 処理を実行する ****************
    final var readDto = checker.read(toDateLocalUtc(20220101), toDateLocalUtc(20220102));

    // **************** 結果を検証する ****************
    assertThat(readDto.deposits()).as("入金データあり").isNotEmpty();
    assertThat(readDto.deposits()).as("所定の入金データが取得される")
        .extracting(Deposit::getId)
        .containsExactly(3L, 2L, 1L);

    assertThat(readDto.dateFrom())
        .as("dateFromの値がNullじゃない")
        .isNotNull();
    assertThat(readDto.dateFrom().getTime())
        .as("dateFromの値が想定通りかどうか")
        .isEqualTo(toDateLocalUtc(20220101).getTime());
  }

  @DisplayName("削除機能テスト")
  @Test
  void deleteTest() {
    // **************** 初期値を設定する ****************
    insertFrequentCryptoDepositUser(masterEM, 1L, "2022-01-01 00:00:00.000");
    insertFrequentCryptoDepositUser(masterEM, 2L, "2022-01-02 00:00:00.000");
    insertFrequentCryptoDepositUser(masterEM, 3L, "2022-01-03 00:00:00.000");


    // **************** 処理を実行する ****************
    checker.delete(toDateLocalUtc(20220102));

    // **************** 結果を検証する ****************
    final var entities = frequentCryptoDepositUserService.findAll();
    assertThat(entities).as("暗号資産高頻度入庫検知データあり").isNotEmpty();
    assertThat(entities).as("所定の暗号資産高頻度入庫検知データが取得される")
        .extracting(FrequentCryptoDepositUser::getId)
        .containsExactly(1L, 3L);
  }

  @DisplayName("更新日がNullのものは取得しない")
  @Test
  void notReadUpdatedAtIsNullTest() {
    // **************** 初期値を設定する ****************
    insertDeposit(masterEM, 1L, Currency.ADA, DepositStatus.DONE, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertDeposit(masterEM, 2L, Currency.ADA, DepositStatus.DONE, "2022-01-01 00:00:00.000", null);
    insertDeposit(masterEM, 3L, Currency.ADA, DepositStatus.DONE, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");


    // **************** 処理を実行する ****************
    final var readDto = checker.read(toDateLocalUtc(20220101), toDateLocalUtc(20220102));

    // **************** 結果を検証する ****************
    assertThat(readDto.deposits()).as("入金データあり").isNotEmpty();
    assertThat(readDto.deposits()).as("所定の入金データが取得される")
        .extracting(Deposit::getId)
        .containsExactly(3L, 1L);
  }

  // 以下Utils関数
  private void insertDeposit(
      EntityManager entityManager,
      Long id,
      Currency currency,
      DepositStatus depositStatus,
      String createdAt,
      String updatedAt) {
    final var updatedAtText = updatedAt == null ? null : "'" + updatedAt + "'";
    final var sql = "insert into deposit (id, user_id, currency, deposit_account_id, deposit_channel, deposit_type,\n"
        + "                     deposit_purpose, amount, asset_amount, fee, jpy_conversion, address,\n"
        + "                     transaction_id, transaction_index, deposit_status, comment, ownertype,\n"
        + "                     recipienttype, last_name, first_name, last_name_kana, first_name_kana,\n"
        + "                     last_name_english, first_name_english, legalname, legalname_kana,\n"
        + "                     legalname_english, addresstype, exchange, area, aregion, purpose, risk_score,\n"
        + "                     transaction_hash, created_at, updated_at)"
        + "values ("
        + id + "," // id
        + "1,"
        + "'" + currency.name() + "',"
        + "1, DEFAULT, null, null, 1.00000000000000000000, null, 1.00000000000000000000, 1.00000000000000000000, 'address', null, null, "
        + "'" + depositStatus.name() + "',"
        + " null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, '" + createdAt + "', "
        + updatedAtText
        + ");";
    executeSql(entityManager, sql);
  }

  private void insertFrequentCryptoDepositUser(
      EntityManager entityManager,
      Long id,
      String targetAt) {
    final var sql = "INSERT INTO frequent_crypto_deposit_user (id, target_at, user_id, deposit_count, tms_status,\n"
        + "                                                   created_at, updated_at)\n"
        + "VALUES (" + id + ", '" + targetAt + "', 1, 3, 'OPEN', '2023-03-07 11:45:58.000',\n"
        + "        '2023-03-07 11:46:01.000');";
    executeSql(entityManager, sql);
  }
}
