package exchange.worker.worker;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

/**
 * @author: wen.y
 * @date: 2024/8/14
 */
@Slf4j
@SpringBootTest(classes = exchange.worker.Application.class)
@ActiveProfiles({"local"})
public class ChainalysisAddressCheckerTest {
	@Resource
	private ChainalysisAddressChecker chainalysisAddressChecker;

	@Test
	public void executeTest() throws Exception {
		chainalysisAddressChecker.execute(null, null);
	}

}
