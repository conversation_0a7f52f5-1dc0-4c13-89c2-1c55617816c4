package exchange.worker.worker;

import static org.assertj.core.api.Assertions.assertThat;

import exchange.common.constant.ReportLabel.WithdrawalStatus;
import exchange.common.entity.WithdrawalAccount;
import exchange.common.service.MultiPrivateWalletWithdrawalUserService;
import javax.persistence.EntityManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@AutoConfigureMockMvc(addFilters = false)
@ActiveProfiles({"test"})
public class MultiPrivateWalletWithdrawalCheckerReadTest extends BaseReaderTest {

  @Autowired
  MultiPrivateWalletWithdrawalChecker checker;

  @Autowired
  MultiPrivateWalletWithdrawalUserService multiPrivateWalletWithdrawalUserService;

  @BeforeEach
  public void beforeEach() {
    // 検知テーブルをリセットする
    executeSql(masterEM, "DELETE FROM multi_private_wallet_withdrawal_user");
    //出金アカウントテーブルをリセットする
    executeSql(masterEM, "DELETE FROM withdrawal_account");
    // 出金テーブルをリセットする
    executeSql(masterEM, "DELETE FROM withdrawal");
    // 出金アカウントテーブルをリセットする
    executeSql(masterEM, "DELETE FROM withdrawal_account");
  }

  @DisplayName("該当出金履歴なし")
  @Test
  void noDateTest() {
    // **************** 初期値を設定する ****************
    // 初期データ なし

    // **************** 処理を実行する ****************
    final var readDto = checker.read(toDate(********), toDate(********));

    // **************** 結果を検証する ****************
    assertThat(readDto.withdrawalAccounts()).as("該当日付に出金なし").isEmpty();
  }

  @DisplayName("adresstypeテスト: 出金があったプライベートウォレットアカウント取得")
  @Test
  void addresstypeTest() {
    // **************** 初期値を設定する ****************

    insertWithdrawal(masterEM, 1L, 1L, WithdrawalStatus.DONE, 1L, "2021-12-31 23:59:59.999",
        "2021-12-31 23:59:59.999");
    insertWithdrawal(masterEM, 2L, 1L, WithdrawalStatus.DONE, 2L, "2021-12-31 23:59:59.999",
        "2021-12-31 23:59:59.999");
    insertWithdrawal(masterEM, 3L, 1L, WithdrawalStatus.DONE, 3L, "2021-12-31 23:59:59.999",
        "2021-12-31 23:59:59.999");
    insertWithdrawal(masterEM, 4L, 2L, WithdrawalStatus.DONE, 4L, "2021-12-31 23:59:59.999",
        "2021-12-31 23:59:59.999");
    insertWithdrawal(masterEM, 5L, 2L, WithdrawalStatus.DONE, 5L, "2021-12-31 23:59:59.999",
        "2021-12-31 23:59:59.999");
    insertWithdrawal(masterEM, 6L, 2L, WithdrawalStatus.DONE, 6L, "2021-12-31 23:59:59.999",
        "2021-12-31 23:59:59.999");
    insertWithdrawal(masterEM, 7L, 3L, WithdrawalStatus.DONE, 7L, "2021-12-31 23:59:59.999",
        "2021-12-31 23:59:59.999");
    insertWithdrawal(masterEM, 8L, 3L, WithdrawalStatus.DONE, 8L, "2021-12-31 23:59:59.999",
        "2021-12-31 23:59:59.999");
    insertWithdrawal(masterEM, 9L, 3L, WithdrawalStatus.DONE, 9L, "2021-12-31 23:59:59.999",
        "2021-12-31 23:59:59.999");
    insertWithdrawal(masterEM, 10L, 3L, WithdrawalStatus.DONE, 10L, "2021-12-31 23:59:59.999",
        "2021-12-31 23:59:59.999");
    insertWithdrawal(masterEM, 11L, 3L, WithdrawalStatus.DONE, 11L, "2021-12-31 23:59:59.999",
        "2021-12-31 23:59:59.999");

    //上記出金と一対一で対応する出金アカウント
    insertWithdrawalAccount(masterEM, 1L,1L,"プライベートウォレット等"); // -> ◯
    insertWithdrawalAccount(masterEM, 2L,1L,"プライベートウォレット等"); // -> ◯
    insertWithdrawalAccount(masterEM, 3L,1L,"プライベートウォレット等"); // -> ◯
    insertWithdrawalAccount(masterEM, 4L,2L,"プライベートウォレット等"); // -> ◯
    insertWithdrawalAccount(masterEM, 5L,2L,"プライベートウォレット等"); // -> ◯
    insertWithdrawalAccount(masterEM, 6L,2L,"国内取引所"); //  -> X 国内取引所のため対象外 X
    insertWithdrawalAccount(masterEM, 7L,3L,"プライベートウォレット等"); // -> ◯
    insertWithdrawalAccount(masterEM, 8L,3L,"プライベートウォレット等"); // -> ◯
    insertWithdrawalAccount(masterEM, 9L,3L,"プライベートウォレット等"); // -> ◯
    insertWithdrawalAccount(masterEM, 10L,3L,"プライベートウォレット等"); // -> ◯
    insertWithdrawalAccount(masterEM, 11L,3L,"国内取引所"); //国内取引所のため対象外 X

    // **************** 処理を実行する ****************
    final var readDto = checker.read(toDateLocalUtc(********), toDateLocalUtc(********));

    // **************** 結果を検証する ****************
    assertThat(readDto.withdrawalAccounts()).as("プライベートウォレットのみ取得").extracting(WithdrawalAccount::getId)
        .containsExactlyInAnyOrder(1L,2L,3L,4L,5L,7L,8L,9L,10L);
  }

  @DisplayName("日付境界テスト: 指定日に出金があったアカウント取得")
  @Test
  void dateTest() {
    // **************** 初期値を設定する ****************
    // 初期データ なし
    insertWithdrawal(masterEM, 1L, 1L, WithdrawalStatus.DONE, 1L, "2021-12-29 00:00:00.000",
        "2021-12-29 23:59:59.999");
    insertWithdrawal(masterEM, 2L, 1L, WithdrawalStatus.DONE, 2L, "2021-12-29 23:59:59.999",
        "2021-12-29 23:59:59.999");
    insertWithdrawal(masterEM, 3L, 1L, WithdrawalStatus.DONE, 3L, "2021-12-30 00:00:00.000",
        "2021-12-30 00:00:00.000");
    insertWithdrawal(masterEM, 4L, 1L, WithdrawalStatus.DONE, 4L, "2021-12-30 23:59:59.999",
        "2021-12-30 23:59:59.999");
    insertWithdrawal(masterEM, 5L, 1L, WithdrawalStatus.DONE, 5L, "2021-12-31 00:00:00.000",
        "2021-12-31 00:00:00.000");
    insertWithdrawal(masterEM, 6L, 1L, WithdrawalStatus.DONE, 6L, "2021-12-31 23:59:59.999",
        "2021-12-31 23:59:59.999");
    insertWithdrawal(masterEM, 7L, 1L, WithdrawalStatus.DONE, 7L, "2022-01-01 00:00:00.000",
        "2022-01-01 00:00:00.000");
    insertWithdrawal(masterEM, 8L, 1L, WithdrawalStatus.DONE, 8L, "2022-01-01 23:59:59.999",
        "2022-01-01 23:59:59.999");
    insertWithdrawal(masterEM, 9L, 1L, WithdrawalStatus.DONE, 9L, "2022-01-02 00:00:00.000",
        "2022-01-02 00:00:00.000");
    insertWithdrawal(masterEM, 10L, 1L, WithdrawalStatus.DONE, 10L, "2022-01-02 23:59:59.999",
        "2022-01-02 23:59:59.999");

    //上記出金withdrawal_account.idと idが一対一で対応する出金アカウント
    insertWithdrawalAccount(masterEM, 1L,1L,"プライベートウォレット等"); // -> 対象日の前々日のため対象外 X
    insertWithdrawalAccount(masterEM, 2L,1L,"プライベートウォレット等"); // -> 対象日の前々日のため対象外 X
    insertWithdrawalAccount(masterEM, 3L,1L,"プライベートウォレット等"); // -> 対象日の前日のため対象外 X
    insertWithdrawalAccount(masterEM, 4L,1L,"プライベートウォレット等"); // -> 対象日の前日のため対象外 X
    insertWithdrawalAccount(masterEM, 5L,1L,"プライベートウォレット等"); // -> 対応する出金データの作成日が"2021-12-31 00:00:00.000" のため対象 ◯
    insertWithdrawalAccount(masterEM, 6L,1L,"プライベートウォレット等"); // -> 対応する出金データの作成日が"2021-12-31 23:59:59.999" のため対象 ◯
    insertWithdrawalAccount(masterEM, 7L,1L,"プライベートウォレット等"); // -> 対象日の次の日のため対象外 X
    insertWithdrawalAccount(masterEM, 8L,1L,"プライベートウォレット等"); // -> 対象日の次の日のため対象外 X
    insertWithdrawalAccount(masterEM, 9L,1L,"プライベートウォレット等"); // -> 対象日の2日後のため対象外 X
    insertWithdrawalAccount(masterEM, 10L,1L,"プライベートウォレット等"); // -> 対象日2日後のため対象外 X

    // **************** 処理を実行する ****************
    final var readDto = checker.read(toDateLocalUtc(********), toDateLocalUtc(********));

    // **************** 結果を検証する ****************
    assertThat(readDto.withdrawalAccounts()).as("指定日に出金があったアカウント").extracting(WithdrawalAccount::getId)
        .containsExactlyInAnyOrder(5L,6L);
  }

  @DisplayName("出金ステータステスト: 出金があった検知対象ステータスのアカウントのみ取得")
  @Test
  void statusTest() {
    // **************** 初期値を設定する ****************
    // 初期データ
    insertWithdrawal(masterEM, 1L, 1L, WithdrawalStatus.AML_INFO_RECIVED, 1L, "2021-12-31 23:59:59.999",
        "2021-12-31 23:59:59.999");
    insertWithdrawal(masterEM, 2L, 1L, WithdrawalStatus.WAITING_SECOND_AML_EXAM, 2L, "2021-12-31 23:59:59.999",
        "2021-12-31 23:59:59.999");
    insertWithdrawal(masterEM, 3L, 1L, WithdrawalStatus.SECOND_AML_EXAMING, 3L, "2021-12-31 23:59:59.999",
        "2021-12-31 23:59:59.999");
    insertWithdrawal(masterEM, 4L, 1L, WithdrawalStatus.WAITING_ADD_INFO, 4L, "2021-12-31 23:59:59.999",
        "2021-12-31 23:59:59.999");
    insertWithdrawal(masterEM, 5L, 1L, WithdrawalStatus.ADD_INFO_CHECKING, 5L, "2021-12-31 23:59:59.999",
        "2021-12-31 23:59:59.999");
    insertWithdrawal(masterEM, 6L, 1L, WithdrawalStatus.REJECTED, 6L, "2021-12-31 23:59:59.999",
        "2021-12-31 23:59:59.999");
    insertWithdrawal(masterEM, 7L, 1L, WithdrawalStatus.FUND_FAILED_RETRY, 7L, "2021-12-31 23:59:59.999",
        "2021-12-31 23:59:59.999");
    insertWithdrawal(masterEM, 8L, 1L, WithdrawalStatus.FUND_FAILED, 8L, "2021-12-31 23:59:59.999",
        "2021-12-31 23:59:59.999");
    insertWithdrawal(masterEM, 9L, 1L, WithdrawalStatus.DONE, 9L, "2021-12-31 23:59:59.999",
        "2021-12-31 23:59:59.999");

    //上記出金withdrawal_account.idと idが一対一で対応する出金アカウント
    insertWithdrawalAccount(masterEM, 1L,1L,"プライベートウォレット等"); // -> 対象外 X
    insertWithdrawalAccount(masterEM, 2L,1L,"プライベートウォレット等"); // -> 対象外 X
    insertWithdrawalAccount(masterEM, 3L,1L,"プライベートウォレット等"); // -> 対象外 X
    insertWithdrawalAccount(masterEM, 4L,1L,"プライベートウォレット等"); // -> 対象外 X
    insertWithdrawalAccount(masterEM, 5L,1L,"プライベートウォレット等"); // -> 対象外 X
    insertWithdrawalAccount(masterEM, 6L,1L,"プライベートウォレット等"); // -> 対象外 X
    insertWithdrawalAccount(masterEM, 7L,1L,"プライベートウォレット等"); // -> 対象外 X
    insertWithdrawalAccount(masterEM, 8L,1L,"プライベートウォレット等"); // -> 対象外 X
    insertWithdrawalAccount(masterEM, 9L,1L,"プライベートウォレット等"); // -> 送金完了は対象のため ◯


    // **************** 処理を実行する ****************
    final var readDto = checker.read(toDateLocalUtc(********), toDateLocalUtc(********));

    // **************** 結果を検証する ****************
    assertThat(readDto.withdrawalAccounts()).as("検知対象ステータスのみ取得").extracting(WithdrawalAccount::getId)
        .containsExactlyInAnyOrder(9L);
  }

  @DisplayName("同一アカウント出金テスト:重複アカウントの出金はカウントしない")
  @Test
  void withdrawalFromSameAccountTest() {
    // **************** 初期値を設定する ****************
    //同一アカウントへの出金
    insertWithdrawal(masterEM, 1L, 1L, WithdrawalStatus.DONE, 1L, "2021-12-31 23:59:59.999",
        "2021-12-31 23:59:59.999");
    insertWithdrawal(masterEM, 2L, 1L, WithdrawalStatus.DONE, 1L, "2021-12-31 23:59:59.999",
        "2021-12-31 23:59:59.999");
    insertWithdrawal(masterEM, 3L, 1L, WithdrawalStatus.DONE, 1L, "2021-12-31 23:59:59.999",
        "2021-12-31 23:59:59.999");

    insertWithdrawalAccount(masterEM, 1L,1L,"プライベートウォレット等");


    // **************** 処理を実行する ****************
    final var readDto = checker.read(toDateLocalUtc(********), toDateLocalUtc(********));

    // **************** 結果を検証する ****************
    // 期待結果: [1,1,1]でなく, [1]
    assertThat(readDto.withdrawalAccounts()).as("").extracting(WithdrawalAccount::getId)
        .containsExactlyInAnyOrder(1L);
  }

  @DisplayName("日付テスト:作成日で検索しているか")
  @Test
  void createdAtCheckTest() {
    // **************** 初期値を設定する ****************
    //同一アカウントへの出金
    insertWithdrawal(masterEM, 1L, 1L, WithdrawalStatus.DONE, 1L, "2021-12-30 23:59:59.999",
        "2021-12-31 23:59:59.999");
    insertWithdrawal(masterEM, 2L, 1L, WithdrawalStatus.DONE, 2L, "2021-12-31 23:59:59.999",
        "2021-12-31 23:59:59.999");
    insertWithdrawal(masterEM, 3L, 1L, WithdrawalStatus.DONE, 3L, "2022-01-01 00:00:00.000",
        "2021-12-31 23:59:59.999");

    insertWithdrawalAccount(masterEM, 1L,1L,"プライベートウォレット等");
    insertWithdrawalAccount(masterEM, 2L,1L,"プライベートウォレット等");
    insertWithdrawalAccount(masterEM, 3L,1L,"プライベートウォレット等");

    // **************** 処理を実行する ****************
    final var readDto = checker.read(toDateLocalUtc(********), toDateLocalUtc(********));

    // **************** 結果を検証する ****************
    // 期待結果: [1,1,1]でなく, [1]
    assertThat(readDto.withdrawalAccounts()).as("").extracting(WithdrawalAccount::getId)
        .containsExactlyInAnyOrder(2L);
  }


  // 以下Utils関数
  private void insertWithdrawal(EntityManager entityManager, Long id, Long userId,
      WithdrawalStatus withdrawalStatus, Long withdrawal_account_id, String createdAt,
      String updatedAt) {
    final var sql =
        "INSERT INTO withdrawal (\n" + "    id, \n" + "    user_id, \n" + "    currency, \n"
            + "    withdrawal_channel, \n" + "    withdrawal_type, \n"
            + "    withdrawal_purpose, \n" + "    amount, \n" + "    asset_amount, \n"
            + "    withdrawal_fee, \n" + "    transaction_fee, \n" + "    jpy_conversion, \n"
            + "    comment, \n" + "    withdrawal_account_id, \n" + "    address, \n"
            + "    transaction_id, \n" + "    withdrawal_status, \n" + "    risk_score, \n"
            + "    failed_number, \n" + "    transaction_hash, \n" + "    created_at, \n"
            + "    updated_at, \n" + "    created_by, \n" + "    updated_by\n" + ") " + "VALUES\n"
            + "(" + id + "," + userId
            + " , 'ADA', 'UNKNOWN', NULL, NULL, 21.00000000000000000000, NULL, 0.00000000000000000000, 0.00000000000000000000, NULL, '', "
            + withdrawal_account_id
            + ", 'addr_test1vrlq3gwzre8kqptuzd2hxm3ghvn6gdy5ndy6cx39vmxcuqcgvh2gk', NULL, '"
            + withdrawalStatus + "', 999, 0, NULL, '" + createdAt + "', '" + updatedAt
            + "', NULL, NULL)\n";
    executeSql(entityManager, sql);
  }

  private void insertWithdrawalAccount(EntityManager entityManager, Long id, Long userId,
      String addressType) {
    final var sql =
        "INSERT INTO withdrawal_account (\n" + "    id, \n" + "    user_id, \n" + "    currency,\n"
            + "    label,\n"
            + "    address,\n" + "    ownertype,\n" + "    recipienttype,\n" + "    last_name,\n"
            + "    first_name,\n" + "    last_name_kana,\n" + "    first_name_kana,\n"
            + "    last_name_english,\n" + "    first_name_english,\n" + "    legalname,\n"
            + "    legalname_kana,\n" + "    legalname_english,\n" + "    addresstype,\n"
            + "    exchange,\n" + "    country,\n" + "    area,\n" + "    purpose,\n"
            + "    destination_tag,\n" + "    enabled\n" + ") VALUES (\n" + id + ",\n" + userId +",\n"
            + "    'ADA',\n" + "    'Main Address',\n"
            + "    '1A1zP1eP5QGefi2DMPTfTL5SLmv7DivfNa',\n" + "    'Individual',\n" + "    NULL,\n"
            + "    'Satoshi',\n" + "    'Nakamoto',\n" + "    'サトシ',\n" + "    'ナカモト',\n"
            + "    'Nakamoto',\n" + "    'Satoshi',\n" + "    NULL,\n" + "    NULL,\n"
            + "    NULL,\n" + "    '" + addressType + "',\n" + "    'Binance',\n" + "    'Japan',\n"
            + "    'Tokyo',\n" + "    'Withdrawal',\n" + "    NULL,\n" + "    1\n" + ");";
    executeSql(entityManager, sql);
  }
};