package exchange.worker.worker;

import exchange.worker.Application;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: wen.y
 * @date: 2024/11/8
 */
@Slf4j
@SpringBootTest(classes = Application.class)
@ActiveProfiles("local")
public class CryptoTokenApplyUpdaterMakerTest {
	@Autowired
	private CryptoTokenApplyUpdaterMaker cryptoTokenApplyUpdaterMaker;

	@Test
	public void test1() throws Exception {
		Map<String, Object> params = new HashMap<>();
//		params.put("csvFileUrl", "http://fs.yuanshengcloud.com/test/update1.csv");
		params.put("csvFileUrl", "/Users/<USER>/Desktop/tmp1/local_905.csv");
//		params.put("onlyCheck", true);
		params.put("csvFileCharset", "UTF-8");
//		params.put("batchSeqNo", 1);
		cryptoTokenApplyUpdaterMaker.execute(null, params);
	}

	@Test
	public void test2() throws Exception {
		Map<String, Object> params = new HashMap<>();
		params.put("taskType", "datafix-902");
		cryptoTokenApplyUpdaterMaker.execute(null, params);
   		 System.out.println("11");
	}

	@Test
	public void test3() throws Exception {
		Map<String, Object> params = new HashMap<>();
		params.put("csvFileUrl", "/Users/<USER>/Desktop/tmp1/local-912fix-1.csv");
		params.put("taskType", "datafix-912");
		params.put("csvFileCharset", "UTF-8");
//		params.put("onlyCheck", true);
		cryptoTokenApplyUpdaterMaker.execute(null, params);
		System.out.println("11");
	}

}
