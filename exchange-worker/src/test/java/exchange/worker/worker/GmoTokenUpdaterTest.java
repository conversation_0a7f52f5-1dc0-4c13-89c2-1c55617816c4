package exchange.worker.worker;

import exchange.worker.Application;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

/**
 * @author: wen.y
 * @date: 2025/2/10
 */
@Slf4j
@SpringBootTest(classes = Application.class)
@ActiveProfiles("local")
public class GmoTokenUpdaterTest {

	@Resource
	private GmoTokenUpdater gmoTokenUpdater;

	@Test
	public void execute() throws Exception {
		gmoTokenUpdater.execute(null, null);
	}
}
