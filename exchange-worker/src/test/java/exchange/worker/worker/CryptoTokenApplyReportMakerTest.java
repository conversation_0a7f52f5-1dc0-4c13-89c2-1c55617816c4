package exchange.worker.worker;

/**
 * @author: wen.y
 * @date: 2024/10/21
 */

import exchange.worker.Application;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

@Slf4j
@SpringBootTest(classes = Application.class)
@ActiveProfiles("local")
public class CryptoTokenApplyReportMakerTest {
	@Autowired
	private CryptoTokenApplyReportMaker cryptoTokenApplyReportMaker;

	@Test
	public void execute() throws Exception {
		cryptoTokenApplyReportMaker.execute(null, null);
	}

}
