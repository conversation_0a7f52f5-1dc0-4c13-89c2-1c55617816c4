package exchange.worker.worker;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.SpyBean;
import exchange.common.constant.Currency;
import exchange.common.entity.Deposit;
import exchange.common.entity.User;
import exchange.common.entity.UserInfo;
import exchange.common.service.DepositService;
import exchange.common.service.UneconomicalCryptoDepositUserService;
import exchange.common.service.UneconomicalCryptoDepositUserService.DepositsByCurrency;
import exchange.common.service.UneconomicalCryptoDepositUserService.ReadData;
import exchange.spot.service.SpotCandlestickAdaJpyService;

public class UneconomicalCryptoDepositCheckerProcessTest extends BaseProcessorTest{


  @Autowired
  UneconomicalCryptoDepositUserService service;

  @SpyBean
  SpotCandlestickAdaJpyService spotCandlestickAdaJpyService;

  @SpyBean
  DepositService depositService;

  @DisplayName("process empty")
  @Test
  void processEmptyTest() {
    // **************** 初期値を設定する ****************
    var users = new ArrayList<User>();
    {
      var user = createPersonalUser(1L, false, "lastName", "firstName");
      var ui = new UserInfo(user.getId());
      user.setUserInfo(ui);
      ui.setIncome(3);
      ui.setFinancialAssets(3);
      users.add(user);
    }

    var deposits = new ArrayList<Deposit>();
    List<DepositsByCurrency> depositsByCurrencies = new ArrayList<DepositsByCurrency>();
    final var depositsByCurrency = new DepositsByCurrency(Currency.ADA,big("1"),deposits);
    depositsByCurrencies.add(depositsByCurrency);

    final var readData = new ReadData(depositsByCurrencies, LocalDate.now(ZoneId.of("Asia/Tokyo")));

    // **************** 処理を実行する ****************
    final var writeDto = service.process(readData);

    // **************** 結果を検証する ****************
    assertThat(writeDto.UneconomicalCryptoDeposit()).as("出力データなし").isEmpty();
  }

  @DisplayName("process zero uneconomical deposit")
  @Test
  void processTwoUneconomicalDepositTest() {
    // **************** 初期値を設定する ****************
    var targetDate = LocalDate.now(ZoneId.of("Asia/Tokyo"));

    var users = new ArrayList<User>();
    {
      var user = createPersonalUser(1L, false, "lastName", "firstName");
      var ui = new UserInfo(user.getId());
      user.setUserInfo(ui);
      users.add(user);
    }
    {
      var user = createPersonalUser(2L, false, "lastName", "firstName");
      var ui = new UserInfo(user.getId());
      user.setUserInfo(ui);
      users.add(user);
    }

    var deposits = new ArrayList<Deposit>();
    {
      var deposit = createDeposit(1L, 1L, Currency.ADA, big("299.9"), big("0"), toDate("2022/02/02 02:02:01"));
      deposit.setUser(users.get(0));
      deposits.add(deposit);
    }
    {
      var deposit = createDeposit(1L, 1L, Currency.ADA, big("299.9"), big("0"), toDate("2022/02/02 02:02:02"));
      deposit.setUser(users.get(1));
      deposits.add(deposit);
    }
    {
      var deposit = createDeposit(1L, 1L, Currency.ADA, big("300"), big("0"), toDate("2022/02/02 02:02:02"));
      deposit.setUser(users.get(1));
      deposits.add(deposit);
    }

    List<DepositsByCurrency> depositsByCurrencies = new ArrayList<DepositsByCurrency>();
    final var depositsByCurrency = new DepositsByCurrency(Currency.ADA,big("1"),deposits);
    depositsByCurrencies.add(depositsByCurrency);
    final var readData = new ReadData(depositsByCurrencies, targetDate);

    // **************** 処理を実行する ****************
    final var writeDto = service.process(readData);

    // **************** 結果を検証する ****************
    assertThat(writeDto.UneconomicalCryptoDeposit()).as("出力データなし").isEmpty();
  }

  @DisplayName("process uneconomical deposit")
  @Test
  void processHitTest() {
    // **************** 初期値を設定する ****************
    var targetDate = LocalDate.now(ZoneId.of("Asia/Tokyo"));

    var users = new ArrayList<User>();
    {
      var user = createPersonalUser(1L, false, "lastName", "firstName");
      var ui = new UserInfo(user.getId());
      user.setUserInfo(ui);
      users.add(user);
    }
    {
      var user = createPersonalUser(2L, false, "lastName", "firstName");
      var ui = new UserInfo(user.getId());
      user.setUserInfo(ui);
      users.add(user);
    }

    var deposits = new ArrayList<Deposit>();
    {
      var deposit = createDeposit(1L, 1L, Currency.ADA, big("299.9"), big("0"), toDate("2022/02/02 02:02:01"));
      deposit.setUser(users.get(0));
      deposits.add(deposit);
    }
    {
      var deposit = createDeposit(1L, 1L, Currency.ADA, big("299.9"), big("0"), toDate("2022/02/02 02:02:02"));
      deposit.setUser(users.get(1));
      deposits.add(deposit);
    }
    {
      var deposit = createDeposit(1L, 1L, Currency.ADA, big("299.9"), big("0"), toDate("2022/02/02 02:02:02"));
      deposit.setUser(users.get(1));
      deposits.add(deposit);
    }
    {
      var deposit = createDeposit(1L, 2L, Currency.ADA, big("299.9"), big("0"), toDate("2022/02/02 02:02:01"));
      deposit.setUser(users.get(0));
      deposits.add(deposit);
    }
    {
      var deposit = createDeposit(1L, 2L, Currency.ADA, big("299.9"), big("0"), toDate("2022/02/02 02:02:02"));
      deposit.setUser(users.get(1));
      deposits.add(deposit);
    }
    {
      var deposit = createDeposit(1L, 2L, Currency.ADA, big("299.9"), big("0"), toDate("2022/02/02 02:02:02"));
      deposit.setUser(users.get(1));
      deposits.add(deposit);
    }
    {
      var deposit = createDeposit(1L, 2L, Currency.ADA, big("299.9"), big("0"), toDate("2022/02/02 02:02:02"));
      deposit.setUser(users.get(1));
      deposits.add(deposit);
    }

    List<DepositsByCurrency> depositsByCurrencies = new ArrayList<DepositsByCurrency>();
    final var depositsByCurrency = new DepositsByCurrency(Currency.ADA,big("1"),deposits);
    depositsByCurrencies.add(depositsByCurrency);
    final var readData = new ReadData(depositsByCurrencies, targetDate);

    // **************** 処理を実行する ****************
    final var writeDto = service.process(readData);

    // **************** 結果を検証する ****************
    var uneconomicalDeposits = writeDto.UneconomicalCryptoDeposit();
    assertEquals(uneconomicalDeposits.size(), 2);
    var user1Deposits = uneconomicalDeposits.stream().filter(d -> d.getUserId() == 1L).collect(Collectors.toList()).get(0);
    var user2Deposits = uneconomicalDeposits.stream().filter(d -> d.getUserId() == 2L).collect(Collectors.toList()).get(0);
    assertEquals(user1Deposits.getTargetAt(), Date.from(targetDate.atStartOfDay(ZoneId.of("Asia/Tokyo")).toInstant()));
    assertEquals(user1Deposits.getDepositCount(), 3);
    assertEquals(user2Deposits.getTargetAt(), Date.from(targetDate.atStartOfDay(ZoneId.of("Asia/Tokyo")).toInstant()));
    assertEquals(user2Deposits.getDepositCount(), 4);
  }

}
