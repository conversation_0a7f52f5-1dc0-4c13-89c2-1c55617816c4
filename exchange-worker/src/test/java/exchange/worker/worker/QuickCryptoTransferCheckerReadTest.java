package exchange.worker.worker;

import static org.assertj.core.api.Assertions.assertThat;

import exchange.common.constant.Currency;
import exchange.common.constant.DepositStatus;
import exchange.common.constant.WithdrawalStatus;
import exchange.common.entity.Deposit;
import exchange.common.entity.QuickCryptoTransferUser;
import exchange.common.entity.Withdrawal;
import exchange.common.service.QuickCryptoTransferUserService;
import javax.persistence.EntityManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@AutoConfigureMockMvc(addFilters = false)
@ActiveProfiles({"test"})
public class QuickCryptoTransferCheckerReadTest extends BaseReaderTest {

  @Autowired
  QuickCryptoTransferChecker checker;

  @Autowired
  QuickCryptoTransferUserService quickCryptoTransferUserService;

  @DisplayName("何も取得されない")
  @Test
  void emptyTest() {
    // **************** 初期値を設定する ****************
    // 初期データなし

    // **************** 処理を実行する ****************
    final var readDto = checker.read(toDate(20220101), toDate(20220102));

    // **************** 結果を検証する ****************
    assertThat(readDto.deposits()).as("入金データなし").isEmpty();
    assertThat(readDto.withdrawals()).as("出金データなし").isEmpty();
    assertThat(readDto.dateFrom())
        .as("dateFromの値がNullじゃない")
        .isNotNull();
    assertThat(readDto.dateFrom().getTime())
        .as("dateFromの値が想定通りかどうか")
        .isEqualTo(toDate(20220101).getTime());
  }

  @DisplayName("入金データ 日付境界検索テスト")
  @Test
  void dateBoundaryDepositsTest() {
    // **************** 初期値を設定する ****************
    insertDeposit(masterEM, 1L, Currency.ADA, DepositStatus.DONE, "2022-01-01 00:00:00.000", "2021-12-31 23:59:59.999");
    insertDeposit(masterEM, 2L, Currency.ADA, DepositStatus.DONE, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertDeposit(masterEM, 3L, Currency.ADA, DepositStatus.DONE, "2022-01-01 00:00:00.000", "2022-01-01 23:59:59.999");
    insertDeposit(masterEM, 4L, Currency.ADA, DepositStatus.DONE, "2022-01-01 00:00:00.000", "2022-01-02 00:00:00.000");
    insertDeposit(masterEM, 5L, Currency.ADA, DepositStatus.DONE, "2022-01-01 00:00:00.000", "2022-01-02 00:00:00.001");


    // **************** 処理を実行する ****************
    final var readDto = checker.read(toDateLocalUtc(20220101), toDateLocalUtc(20220102));

    // **************** 結果を検証する ****************
    assertThat(readDto.deposits()).as("入金データあり").isNotEmpty();
    assertThat(readDto.deposits()).as("所定の入金データが取得される")
        .extracting(Deposit::getId)
        .containsExactly(3L, 2L);
  }

  @DisplayName("出金データ 日付境界検索テスト")
  @Test
  void dateBoundaryWithdrawalsTest() {
    // **************** 初期値を設定する ****************
    insertWithdrawal(masterEM, 1L, Currency.ADA, WithdrawalStatus.DONE, "2021-12-31 23:59:59.999", "2022-01-01 00:00:00.000");
    insertWithdrawal(masterEM, 2L, Currency.ADA, WithdrawalStatus.DONE, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertWithdrawal(masterEM, 3L, Currency.ADA, WithdrawalStatus.DONE, "2022-01-01 23:59:59.999", "2022-01-01 00:00:00.000");
    insertWithdrawal(masterEM, 4L, Currency.ADA, WithdrawalStatus.DONE, "2022-01-02 00:00:00.000", "2022-01-01 00:00:00.000");
    insertWithdrawal(masterEM, 5L, Currency.ADA, WithdrawalStatus.DONE, "2022-01-02 00:00:00.001", "2022-01-01 00:00:00.000");


    // **************** 処理を実行する ****************
    final var readDto = checker.read(toDateLocalUtc(20220101), toDateLocalUtc(20220102));

    // **************** 結果を検証する ****************
    assertThat(readDto.withdrawals()).as("出庫データあり").isNotEmpty();
    assertThat(readDto.withdrawals()).as("所定の出庫データが取得される")
        .extracting(Withdrawal::getId)
        .containsExactly(3L, 2L);
  }

  @DisplayName("入金ステータステスト")
  @Test
  void depositStatusTest() {
    // **************** 初期値を設定する ****************
    insertDeposit(masterEM, 1L, Currency.ADA, DepositStatus.DONE, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertDeposit(masterEM, 2L, Currency.ADA, DepositStatus.CONFIRMED, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertDeposit(masterEM, 3L, Currency.ADA, DepositStatus.REJECTED_FUNDED, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");


    // **************** 処理を実行する ****************
    final var readDto = checker.read(toDateLocalUtc(20220101), toDateLocalUtc(20220102));

    // **************** 結果を検証する ****************
    assertThat(readDto.deposits()).as("入金データあり").isNotEmpty();
    assertThat(readDto.deposits()).as("所定の入金データが取得される")
        .extracting(Deposit::getId)
        .containsExactly(1L);
  }

  @DisplayName("出金ステータステスト")
  @Test
  void withdrawalStatusTest() {
    // **************** 初期値を設定する ****************
    insertWithdrawal(masterEM, 1L, Currency.ADA, WithdrawalStatus.BROADCASTED, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertWithdrawal(masterEM, 2L, Currency.ADA, WithdrawalStatus.FINISHING, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertWithdrawal(masterEM, 3L, Currency.ADA, WithdrawalStatus.TRANSACTION_ERROR, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertWithdrawal(masterEM, 4L, Currency.ADA, WithdrawalStatus.SYSTEM_ERROR, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertWithdrawal(masterEM, 5L, Currency.ADA, WithdrawalStatus.EXAMINING, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertWithdrawal(masterEM, 6L, Currency.ADA, WithdrawalStatus.AML_INFO_RECIVED, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertWithdrawal(masterEM, 7L, Currency.ADA, WithdrawalStatus.FIRST_AML_EXAMING, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertWithdrawal(masterEM, 8L, Currency.ADA, WithdrawalStatus.WAITING_SECOND_AML_EXAM, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertWithdrawal(masterEM, 9L, Currency.ADA, WithdrawalStatus.SECOND_AML_EXAMING, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertWithdrawal(masterEM, 10L, Currency.ADA, WithdrawalStatus.APPROVED, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertWithdrawal(masterEM, 11L, Currency.ADA, WithdrawalStatus.WAITING_ADD_INFO, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertWithdrawal(masterEM, 12L, Currency.ADA, WithdrawalStatus.ADD_INFO_CHECKING, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertWithdrawal(masterEM, 13L, Currency.ADA, WithdrawalStatus.REJECTED, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertWithdrawal(masterEM, 14L, Currency.ADA, WithdrawalStatus.FUNDING, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertWithdrawal(masterEM, 15L, Currency.ADA, WithdrawalStatus.DONE, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertWithdrawal(masterEM, 16L, Currency.ADA, WithdrawalStatus.FUND_FAILED_RETRY, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertWithdrawal(masterEM, 17L, Currency.ADA, WithdrawalStatus.FUND_FAILED, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");



    // **************** 処理を実行する ****************
    final var readDto = checker.read(toDateLocalUtc(20220101), toDateLocalUtc(20220102));

    // **************** 結果を検証する ****************
    assertThat(readDto.withdrawals()).as("出庫データあり").isNotEmpty();
    assertThat(readDto.withdrawals()).as("所定の出庫データが取得される")
        .extracting(Withdrawal::getId)
        .containsExactly(15L, 14L, 10L, 5L);
  }

  @DisplayName("入金 通貨別取得テスト")
  @Test
  void depositCurrencyTest() {
    // **************** 初期値を設定する ****************
    insertDeposit(masterEM, 1L, Currency.ADA, DepositStatus.DONE, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertDeposit(masterEM, 2L, Currency.JPY, DepositStatus.DONE, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertDeposit(masterEM, 3L, Currency.NIDT, DepositStatus.DONE, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");


    // **************** 処理を実行する ****************
    final var readDto = checker.read(toDateLocalUtc(20220101), toDateLocalUtc(20220102));

    // **************** 結果を検証する ****************
    assertThat(readDto.deposits()).as("入金データあり").isNotEmpty();
    assertThat(readDto.deposits()).as("所定の入金データが取得される")
        .extracting(Deposit::getId)
        .containsExactly(3L, 2L, 1L);
  }

  @DisplayName("出庫 通貨別取得テスト")
  @Test
  void withdrawalCurrencyTest() {
    // **************** 初期値を設定する ****************
    insertWithdrawal(masterEM, 1L, Currency.ADA, WithdrawalStatus.DONE, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertWithdrawal(masterEM, 2L, Currency.JPY, WithdrawalStatus.DONE, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertWithdrawal(masterEM, 3L, Currency.NIDT, WithdrawalStatus.DONE, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");


    // **************** 処理を実行する ****************
    final var readDto = checker.read(toDateLocalUtc(20220101), toDateLocalUtc(20220102));

    // **************** 結果を検証する ****************
    assertThat(readDto.withdrawals()).as("出庫データあり").isNotEmpty();
    assertThat(readDto.withdrawals()).as("所定の出庫データが取得される")
        .extracting(Withdrawal::getId)
        .containsExactly(3L, 2L, 1L);
  }

  @BeforeEach
  public void beforeEach() {
    // 入庫後短期出金検知テーブルをリセットする
    executeSql(masterEM, "DELETE FROM quick_crypto_transfer_user");

    // 暗号資産入金テーブルをリセットする
    executeSql(masterEM, "DELETE FROM deposit");

    // 暗号資産出金テーブルをリセットする
    executeSql(masterEM, "DELETE FROM withdrawal");
  }

  @DisplayName("削除機能テスト")
  @Test
  void deleteTest() {
    // **************** 初期値を設定する ****************
    insertQuickCryptoTransferUser(masterEM, 1L, "2022-01-01 00:00:00.000");
    insertQuickCryptoTransferUser(masterEM, 2L, "2022-01-02 00:00:00.000");
    insertQuickCryptoTransferUser(masterEM, 3L, "2022-01-03 00:00:00.000");


    // **************** 処理を実行する ****************
    checker.delete(toDateLocalUtc(20220102));

    // **************** 結果を検証する ****************
    final var entities = quickCryptoTransferUserService.findAll();
    assertThat(entities).as("入庫後短期出庫検知データあり").isNotEmpty();
    assertThat(entities).as("所定の入庫後短期出庫検知データが取得される")
        .extracting(QuickCryptoTransferUser::getId)
        .containsExactly(1L, 3L);
  }

  // 以下Utils関数
  private void insertDeposit(
      EntityManager entityManager,
      Long id,
      Currency currency,
      DepositStatus depositStatus,
      String createdAt,
      String updatedAt) {
    final var sql = "insert into deposit (id, user_id, currency, deposit_account_id, deposit_channel, deposit_type,\n"
        + "                     deposit_purpose, amount, asset_amount, fee, jpy_conversion, address,\n"
        + "                     transaction_id, transaction_index, deposit_status, comment, ownertype,\n"
        + "                     recipienttype, last_name, first_name, last_name_kana, first_name_kana,\n"
        + "                     last_name_english, first_name_english, legalname, legalname_kana,\n"
        + "                     legalname_english, addresstype, exchange, area, aregion, purpose, risk_score,\n"
        + "                     transaction_hash, created_at, updated_at)"
        + "values ("
        + id + "," // id
        + "1,"
        + "'" + currency.name() + "',"
        + "1, DEFAULT, null, null, 1.********************, null, 1.********************, 1.********************, 'address', null, null, "
        + "'" + depositStatus.name() + "',"
        + " null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, '" + createdAt + "', "
        + "'" + updatedAt + "'"
        + ");";
    executeSql(entityManager, sql);
  }

  private void insertWithdrawal(
      EntityManager entityManager,
      Long id,
      Currency currency,
      WithdrawalStatus withdrawalStatus,
      String createdAt,
      String updatedAt) {
    final var sql = "INSERT INTO exchange.withdrawal (id, user_id, currency, withdrawal_channel, withdrawal_type, withdrawal_purpose, amount, asset_amount, withdrawal_fee, transaction_fee, jpy_conversion, comment, withdrawal_account_id, address, transaction_id, withdrawal_status, risk_score, failed_number, transaction_hash, created_at, updated_at, created_by, updated_by) "
        + "VALUES ("
        + id + "," // id
        + "1,"
        + "'" + currency.name() + "',"
        + "DEFAULT, null, null, 1.********************, 0.**********, 0.********************, 0.********************, null, null, 1, 'address', null,"
        + "'" + withdrawalStatus.name() + "',"
        + "null, DEFAULT, null,"
        + "'" + createdAt + "',"
        + "'" + updatedAt + "',"
        + " null, null);";
    executeSql(entityManager, sql);
  }

  private void insertQuickCryptoTransferUser(
      EntityManager entityManager,
      Long id,
      String targetAt) {
    final var sql = "INSERT INTO quick_crypto_transfer_user (id, target_at, user_id, tms_status,\n"
        + "                                                   created_at, updated_at)\n"
        + "VALUES (" + id + ", '" + targetAt + "', 1, 'OPEN', '2023-03-07 11:45:58.000',\n"
        + "        '2023-03-07 11:46:01.000');";
    executeSql(entityManager, sql);
  }
}
