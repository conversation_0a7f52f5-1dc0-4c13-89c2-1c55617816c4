package exchange.worker.worker;

import static org.assertj.core.api.Assertions.assertThat;

import exchange.common.constant.Currency;
import exchange.common.constant.DepositStatus;
import exchange.common.constant.FiatWithdrawalStatus;
import exchange.common.entity.Deposit;
import exchange.common.entity.FiatWithdrawal;
import exchange.common.entity.QuickCryptoToJpyUser;
import exchange.common.service.QuickCryptoToJpyUserService;
import javax.persistence.EntityManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@AutoConfigureMockMvc(addFilters = false)
@ActiveProfiles({"test"})
public class QuickCryptoToJpyCheckerReadTest extends BaseReaderTest {

  @Autowired
  QuickCryptoToJpyChecker checker;

  @Autowired
  QuickCryptoToJpyUserService quickCryptoToJpyUserService;

  @BeforeEach
  public void beforeEach() {
    // 入庫後短期出金検知テーブルをリセットする
    executeSql(masterEM, "DELETE FROM quick_crypto_to_jpy_user");

    // 入金テーブルをリセットする
    executeSql(masterEM, "DELETE FROM deposit");

    // 日本円出金テーブルをリセットする
    executeSql(masterEM, "DELETE FROM fiat_withdrawal");
  }

  @DisplayName("何も取得されない")
  @Test
  void emptyTest() {
    // **************** 初期値を設定する ****************
    // 初期データなし

    // **************** 処理を実行する ****************
    final var readDto = checker.read(toDate(20220101), toDate(20220102));

    // **************** 結果を検証する ****************
    assertThat(readDto.deposits()).as("入金データなし").isEmpty();
    assertThat(readDto.fiatWithdrawals()).as("出金データなし").isEmpty();
    assertThat(readDto.dateFrom())
        .as("dateFromの値がNullじゃない")
        .isNotNull();
    assertThat(readDto.dateFrom().getTime())
        .as("dateFromの値が想定通りかどうか")
        .isEqualTo(toDate(20220101).getTime());
  }

  @DisplayName("入金データ 日付境界検索テスト")
  @Test
  void dateBoundaryDepositsTest() {
    // **************** 初期値を設定する ****************
    insertDeposit(masterEM, 1L, Currency.ADA, DepositStatus.DONE, "2022-01-01 00:00:00.000", "2021-12-31 23:59:59.999");
    insertDeposit(masterEM, 2L, Currency.ADA, DepositStatus.DONE, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertDeposit(masterEM, 3L, Currency.ADA, DepositStatus.DONE, "2022-01-01 00:00:00.000", "2022-01-01 23:59:59.999");
    insertDeposit(masterEM, 4L, Currency.ADA, DepositStatus.DONE, "2022-01-02 00:00:00.000", "2022-01-02 00:00:00.000");
    insertDeposit(masterEM, 5L, Currency.ADA, DepositStatus.DONE, "2022-01-01 00:00:00.000", "2022-01-02 00:00:00.001");


    // **************** 処理を実行する ****************
    final var readDto = checker.read(toDateLocalUtc(20220101), toDateLocalUtc(20220102));

    // **************** 結果を検証する ****************
    assertThat(readDto.deposits()).as("入金データあり").isNotEmpty();
    assertThat(readDto.deposits()).as("所定の入金データが取得される")
        .extracting(Deposit::getId)
        .containsExactly(3L, 2L);

    assertThat(readDto.dateFrom())
        .as("dateFromの値がNullじゃない")
        .isNotNull();
    assertThat(readDto.dateFrom().getTime())
        .as("dateFromの値が想定通りかどうか")
        .isEqualTo(toDateLocalUtc(20220101).getTime());
  }

  @DisplayName("出金データ 日付境界検索テスト")
  @Test
  void dateBoundaryFiatWithdrawalsTest() {
    // **************** 初期値を設定する ****************
    insertFiatWithdrawal(masterEM, 1L, FiatWithdrawalStatus.DONE, "2021-12-31 23:59:59.999", "2022-01-01 00:00:00.000");
    insertFiatWithdrawal(masterEM, 2L, FiatWithdrawalStatus.DONE, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertFiatWithdrawal(masterEM, 3L, FiatWithdrawalStatus.DONE, "2022-01-01 23:59:59.999", "2022-01-01 00:00:00.000");
    insertFiatWithdrawal(masterEM, 4L, FiatWithdrawalStatus.DONE, "2022-01-02 00:00:00.000", "2022-01-01 00:00:00.000");
    insertFiatWithdrawal(masterEM, 5L, FiatWithdrawalStatus.DONE, "2022-01-02 00:00:00.001", "2022-01-01 00:00:00.000");


    // **************** 処理を実行する ****************
    final var readDto = checker.read(toDateLocalUtc(20220101), toDateLocalUtc(20220102));

    // **************** 結果を検証する ****************
    assertThat(readDto.fiatWithdrawals()).as("出金データあり").isNotEmpty();
    assertThat(readDto.fiatWithdrawals()).as("所定の出金データが取得される")
        .extracting(FiatWithdrawal::getId)
        .containsExactly(3L, 2L);

    assertThat(readDto.dateFrom())
        .as("dateFromの値がNullじゃない")
        .isNotNull();
    assertThat(readDto.dateFrom().getTime())
        .as("dateFromの値が想定通りかどうか")
        .isEqualTo(toDateLocalUtc(20220101).getTime());
  }

  @DisplayName("入金ステータステスト")
  @Test
  void depositStatusTest() {
    // **************** 初期値を設定する ****************
    insertDeposit(masterEM, 1L, Currency.ADA, DepositStatus.DONE, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertDeposit(masterEM, 2L, Currency.ADA, DepositStatus.CONFIRMED, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertDeposit(masterEM, 3L, Currency.ADA, DepositStatus.REJECTED_FUNDED, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");


    // **************** 処理を実行する ****************
    final var readDto = checker.read(toDateLocalUtc(20220101), toDateLocalUtc(20220102));

    // **************** 結果を検証する ****************
    assertThat(readDto.deposits()).as("入金データあり").isNotEmpty();
    assertThat(readDto.deposits()).as("所定の入金データが取得される")
        .extracting(Deposit::getId)
        .containsExactly(1L);
  }

  @DisplayName("出金ステータステスト")
  @Test
  void fiatWithdrawalStatusTest() {
    // **************** 初期値を設定する ****************
    insertFiatWithdrawal(masterEM, 1L, FiatWithdrawalStatus.DONE, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertFiatWithdrawal(masterEM, 2L, FiatWithdrawalStatus.APPROVING, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertFiatWithdrawal(masterEM, 3L, FiatWithdrawalStatus.REJECTED, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertFiatWithdrawal(masterEM, 4L, FiatWithdrawalStatus.SYSTEM_ERROR, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertFiatWithdrawal(masterEM, 5L, FiatWithdrawalStatus.TRANSACTION_ERROR, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertFiatWithdrawal(masterEM, 6L, FiatWithdrawalStatus.WAITTING, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");


    // **************** 処理を実行する ****************
    final var readDto = checker.read(toDateLocalUtc(20220101), toDateLocalUtc(20220102));

    // **************** 結果を検証する ****************
    assertThat(readDto.fiatWithdrawals()).as("出金データあり").isNotEmpty();
    assertThat(readDto.fiatWithdrawals()).as("所定の出金データが取得される")
        .extracting(FiatWithdrawal::getId)
        .containsExactly(6L, 2L, 1L);
  }

  @DisplayName("通貨別取得テスト")
  @Test
  void currencyTest() {
    // **************** 初期値を設定する ****************
    insertDeposit(masterEM, 1L, Currency.ADA, DepositStatus.DONE, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertDeposit(masterEM, 2L, Currency.JPY, DepositStatus.DONE, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");
    insertDeposit(masterEM, 3L, Currency.NIDT, DepositStatus.DONE, "2022-01-01 00:00:00.000", "2022-01-01 00:00:00.000");


    // **************** 処理を実行する ****************
    final var readDto = checker.read(toDateLocalUtc(20220101), toDateLocalUtc(20220102));

    // **************** 結果を検証する ****************
    assertThat(readDto.deposits()).as("入金データあり").isNotEmpty();
    assertThat(readDto.deposits()).as("所定の入金データが取得される")
        .extracting(Deposit::getId)
        .containsExactly(3L, 2L, 1L);

    assertThat(readDto.dateFrom())
        .as("dateFromの値がNullじゃない")
        .isNotNull();
    assertThat(readDto.dateFrom().getTime())
        .as("dateFromの値が想定通りかどうか")
        .isEqualTo(toDateLocalUtc(20220101).getTime());
  }

  @DisplayName("削除機能テスト")
  @Test
  void deleteTest() {
    // **************** 初期値を設定する ****************
    insertQuickCryptoToJpyUser(masterEM, 1L, "2022-01-01 00:00:00.000");
    insertQuickCryptoToJpyUser(masterEM, 2L, "2022-01-02 00:00:00.000");
    insertQuickCryptoToJpyUser(masterEM, 3L, "2022-01-03 00:00:00.000");


    // **************** 処理を実行する ****************
    checker.delete(toDateLocalUtc(20220102));

    // **************** 結果を検証する ****************
    final var entities = quickCryptoToJpyUserService.findAll();
    assertThat(entities).as("入庫後短期出金検知データあり").isNotEmpty();
    assertThat(entities).as("所定の入庫後短期出金検知データが取得される")
        .extracting(QuickCryptoToJpyUser::getId)
        .containsExactly(1L, 3L);
  }

  // 以下Utils関数
  private void insertDeposit(
      EntityManager entityManager,
      Long id,
      Currency currency,
      DepositStatus depositStatus,
      String createdAt,
      String updatedAt) {
    final var sql = "insert into deposit (id, user_id, currency, deposit_account_id, deposit_channel, deposit_type,\n"
        + "                     deposit_purpose, amount, asset_amount, fee, jpy_conversion, address,\n"
        + "                     transaction_id, transaction_index, deposit_status, comment, ownertype,\n"
        + "                     recipienttype, last_name, first_name, last_name_kana, first_name_kana,\n"
        + "                     last_name_english, first_name_english, legalname, legalname_kana,\n"
        + "                     legalname_english, addresstype, exchange, area, aregion, purpose, risk_score,\n"
        + "                     transaction_hash, created_at, updated_at)"
        + "values ("
        + id + "," // id
        + "1,"
        + "'" + currency.name() + "',"
        + "1, DEFAULT, null, null, 1.00000000000000000000, null, 1.00000000000000000000, 1.00000000000000000000, 'address', null, null, "
        + "'" + depositStatus.name() + "',"
        + " null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, '" + createdAt + "', "
        + "'" + updatedAt + "'"
        + ");";
    executeSql(entityManager, sql);
  }

  private void insertFiatWithdrawal(
      EntityManager entityManager,
      Long id,
      FiatWithdrawalStatus fiatWithdrawalStatus,
      String createdAt,
      String updatedAt) {
    final var sql = "insert into fiat_withdrawal (id, user_id, bank_account_id, amount, fee, fiat_withdrawal_status,\n"
        + "                             comment, apply_no, created_at, updated_at, created_by, updated_by)"
        + "values ("
        + id + "," // id
        + "1, 1, 0, 0,"
        + "'" + fiatWithdrawalStatus.name() + "',"
        + "null, null, "
        + "'" + createdAt + "',"
        + "'" + updatedAt + "',"
        + "null, null"
        + ");";
    executeSql(entityManager, sql);
  }

  private void insertQuickCryptoToJpyUser(
      EntityManager entityManager,
      Long id,
      String targetAt) {
    final var sql = "INSERT INTO quick_crypto_to_jpy_user (id, target_at, user_id, tms_status,\n"
        + "                                                   created_at, updated_at)\n"
        + "VALUES (" + id + ", '" + targetAt + "', 1, 'OPEN', '2023-03-07 11:45:58.000',\n"
        + "        '2023-03-07 11:46:01.000');";
    executeSql(entityManager, sql);
  }
}
