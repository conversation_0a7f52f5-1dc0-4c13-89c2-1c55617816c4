package exchange.worker.worker;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.SpyBean;
import exchange.common.constant.Currency;
import exchange.common.entity.Deposit;
import exchange.common.entity.User;
import exchange.common.entity.UserInfo;
import exchange.common.service.DepositService;
import exchange.common.service.ExcessiveDepositByPeriodService;
import exchange.common.service.ExcessiveDepositByPeriodService.DepositsByCurrency;
import exchange.common.service.ExcessiveDepositByPeriodService.ReadData;
import exchange.spot.service.SpotCandlestickAdaJpyService;

public class ExcessiveDepositByPeriodCheckerProcessTest extends BaseProcessorTest{


  @Autowired
  ExcessiveDepositByPeriodService service;

  @SpyBean
  SpotCandlestickAdaJpyService spotCandlestickAdaJpyService;

  @SpyBean
  DepositService depositService;

  @DisplayName("process empty - zero deposits")
  @Test
  void processEmptyZeroDepositsTest() {
    // **************** 初期値を設定する ****************
    var users = new ArrayList<User>();
    {
      var user = createPersonalUser(1L, false, "lastName", "firstName");
      var ui = new UserInfo(user.getId());
      user.setUserInfo(ui);
      ui.setIncome(3);
      ui.setFinancialAssets(3);
      users.add(user);
    }
    {
      var user = createPersonalUser(2L, false, "lastName", "firstName");
      var ui = new UserInfo(user.getId());
      user.setUserInfo(ui);
      ui.setIncome(3);
      ui.setFinancialAssets(3);
      users.add(user);
    }

    var deposits = new ArrayList<Deposit>();

    final var readData = new ReadData(
      List.of(new DepositsByCurrency(Currency.ADA, big("100"), deposits)),
      LocalDate.now(ZoneId.of("Asia/Tokyo"))
    );

    // **************** 処理を実行する ****************
    final var writeDtos = service.process(readData);

    // **************** 結果を検証する ****************
    assertThat(writeDtos.ExcessiveDeposits()).as("出力データなし").isEmpty();
  }

  @DisplayName("process empty - amount * jpyPrice < 2000000")
  @Test
  void processEmptyNotExcessive() {
    // **************** 初期値を設定する ****************
    var users = new ArrayList<User>();
    {
      var user = createPersonalUser(1L, false, "lastName", "firstName");
      var ui = new UserInfo(user.getId());
      user.setUserInfo(ui);
      ui.setIncome(2); // 100万円
      ui.setFinancialAssets(2); // 100万円
      users.add(user);
    }

    var deposits = new ArrayList<Deposit>();
    {
      var deposit = createDeposit(1L, 1L, Currency.ADA, big("1999999"), big("2"), toDate("2022/02/02 02:02:01"));
      deposit.setUser(users.get(0));
      deposits.add(deposit);
    }

    final var readData = new ReadData(
      List.of(new DepositsByCurrency(Currency.ADA, big("1"), deposits)),
      LocalDate.now(ZoneId.of("Asia/Tokyo"))
    );

    // **************** 処理を実行する ****************
    final var writeDtos = service.process(readData);

    // **************** 結果を検証する ****************
    assertThat(writeDtos.ExcessiveDeposits()).as("出力データなし").isEmpty();
  }

  @DisplayName("process empty - amount * jpyPrice <= 3000000 & income 3000000")
  @Test
  void processEmptyNotExcessive300() {
    // **************** 初期値を設定する ****************
    var users = new ArrayList<User>();
    {
      var user = createPersonalUser(1L, false, "lastName", "firstName");
      var ui = new UserInfo(user.getId());
      user.setUserInfo(ui);
      ui.setIncome(3); // 300万円
      ui.setFinancialAssets(3); // 300万円
      users.add(user);
    }

    var deposits = new ArrayList<Deposit>();
    {
      var deposit = createDeposit(1L, 1L, Currency.ADA, big("1000000"), big("2"), toDate("2022/02/02 02:02:01"));
      deposit.setUser(users.get(0));
      deposits.add(deposit);
    }
    {
      var deposit = createDeposit(1L, 1L, Currency.ADA, big("2000000"), big("2"), toDate("2022/02/03 02:02:01"));
      deposit.setUser(users.get(0));
      deposits.add(deposit);
    }

    final var readData = new ReadData(
      List.of(new DepositsByCurrency(Currency.ADA, big("1"), deposits)),
      LocalDate.now(ZoneId.of("Asia/Tokyo"))
    );

    // **************** 処理を実行する ****************
    final var writeDtos = service.process(readData);

    // **************** 結果を検証する ****************
    assertThat(writeDtos.ExcessiveDeposits()).as("出力データなし").isEmpty();
  }

  @DisplayName("process empty - amount * jpyPrice = 3000000 & income 3000000")
  @Test
  void processEmptyNotExcessiveIncome() {
    // **************** 初期値を設定する ****************
    var users = new ArrayList<User>();
    {
      var user = createPersonalUser(1L, false, "lastName", "firstName");
      var ui = new UserInfo(user.getId());
      user.setUserInfo(ui);
      ui.setIncome(3); // 300万円
      ui.setFinancialAssets(4); // 500万円
      users.add(user);
    }

    var deposits = new ArrayList<Deposit>();
    {
      var deposit = createDeposit(1L, 1L, Currency.ADA, big("2000000"), big("2"), toDate("2022/02/02 02:02:01"));
      deposit.setUser(users.get(0));
      deposits.add(deposit);
    }
    {
      var deposit = createDeposit(1L, 1L, Currency.ADA, big("1000000"), big("2"), toDate("2022/02/03 02:02:01"));
      deposit.setUser(users.get(0));
      deposits.add(deposit);
    }

    final var readData = new ReadData(
      List.of(new DepositsByCurrency(Currency.ADA, big("1"), deposits)),
      LocalDate.now(ZoneId.of("Asia/Tokyo"))
    );

    // **************** 処理を実行する ****************
    final var writeDto = service.process(readData);

    // **************** 結果を検証する ****************
    assertThat(writeDto.ExcessiveDeposits()).as("出力データなし").isEmpty();
  }

  @DisplayName("process empty - amount * jpyPrice = 3000001 & income 3000000")
  @Test
  void processExcessiveIncome() {
    // **************** 初期値を設定する ****************
    var users = new ArrayList<User>();
    {
      var user = createPersonalUser(1L, false, "lastName", "firstName");
      var ui = new UserInfo(user.getId());
      user.setUserInfo(ui);
      ui.setIncome(2); // 300万円
      ui.setFinancialAssets(3); // 500万円
      users.add(user);
    }

    var deposits = new ArrayList<Deposit>();
    {
      var deposit = createDeposit(1L, 1L, Currency.ADA, big("2000000"), big("2"), toDate("2022/02/02 02:02:01"));
      deposit.setUser(users.get(0));
      deposits.add(deposit);
    }
    {
      var deposit = createDeposit(1L, 1L, Currency.ADA, big("1000001"), big("2"), toDate("2022/02/03 02:02:01"));
      deposit.setUser(users.get(0));
      deposits.add(deposit);
    }

    final var readData = new ReadData(
      List.of(new DepositsByCurrency(Currency.ADA, big("1"), deposits)),
      LocalDate.now(ZoneId.of("Asia/Tokyo"))
    );

    // **************** 処理を実行する ****************
    final var writeDto = service.process(readData);

    // **************** 結果を検証する ****************
    var excessiveDeposits = writeDto.ExcessiveDeposits();
    assertEquals(excessiveDeposits.size(), 1);
  }

  @DisplayName("process hit - amount * jpyPrice = 2000000 & financial 1000000")
  @Test
  void processExcessiveFinancialAsset() {
    // **************** 初期値を設定する ****************
    var users = new ArrayList<User>();
    {
      var user = createPersonalUser(1L, false, "lastName", "firstName");
      var ui = new UserInfo(user.getId());
      user.setUserInfo(ui);
      ui.setIncome(2); // 300万円
      ui.setFinancialAssets(1); // 100万円
      users.add(user);
    }

    var deposits = new ArrayList<Deposit>();
    {
      var deposit = createDeposit(1L, 1L, Currency.ADA, big("1000000"), big("2"), toDate("2022/02/02 02:02:01"));
      deposit.setUser(users.get(0));
      deposits.add(deposit);
    }
    {
      var deposit = createDeposit(1L, 1L, Currency.ADA, big("1000000"), big("2"), toDate("2022/02/03 02:02:01"));
      deposit.setUser(users.get(0));
      deposits.add(deposit);
    }

    final var readData = new ReadData(
      List.of(new DepositsByCurrency(Currency.ADA, big("1"), deposits)),
      LocalDate.now(ZoneId.of("Asia/Tokyo"))
    );

    // **************** 処理を実行する ****************
    final var writeDto = service.process(readData);

    // **************** 結果を検証する ****************
    var excessiveDeposits = writeDto.ExcessiveDeposits();
    assertEquals(excessiveDeposits.size(), 1);
  }

  @DisplayName("process hit")
  @Test
  void processHitTest() {
    // **************** 初期値を設定する ****************
    var targetDate = LocalDate.now(ZoneId.of("Asia/Tokyo"));

    var users = new ArrayList<User>();
    {
      var user = createPersonalUser(1L, false, "lastName", "firstName");
      var ui = new UserInfo(user.getId());
      user.setUserInfo(ui);
      ui.setIncome(1); // see getIncomeConverter: 1,000,000円
      ui.setFinancialAssets(2);
      users.add(user);
    }
    {
      var user = createPersonalUser(2L, false, "lastName", "firstName");
      var ui = new UserInfo(user.getId());
      user.setUserInfo(ui);
      ui.setIncome(2); // see getIncomeConverter: 3,000,000円
      ui.setFinancialAssets(2);
      users.add(user);
    }

    var deposits = new ArrayList<Deposit>();
    {
      var deposit = createDeposit(1L, 1L, Currency.ADA, big("100"), big("0"), toDate("2022/02/02 02:02:01"));
      deposit.setUser(users.get(0));
      deposits.add(deposit);
    }

    {
      var deposit = createDeposit(2L, 1L, Currency.ADA, big("100"), big("0"), toDate("2022/02/02 02:02:01"));
      deposit.setUser(users.get(0));
      deposits.add(deposit);
    }
    {
      var deposit = createDeposit(3L, 2L, Currency.ADA, big("300.1"), big("0"), toDate("2022/02/02 02:02:02"));
      deposit.setUser(users.get(1));
      deposits.add(deposit);
    }

    final var readData = new ReadData(
      List.of(new DepositsByCurrency(Currency.ADA, big("10000"), deposits)),
      LocalDate.now(ZoneId.of("Asia/Tokyo"))
    );

    // **************** 処理を実行する ****************
    final var writeDto = service.process(readData);

    // **************** 結果を検証する ****************
    var excessiveDeposits = writeDto.ExcessiveDeposits();
    assertEquals(excessiveDeposits.size(), 2);
    assertEquals(excessiveDeposits.get(0).getUserId(), 1L);
    assertEquals(excessiveDeposits.get(0).getTargetAt(), Date.from(targetDate.atStartOfDay(ZoneId.of("Asia/Tokyo")).toInstant()));
    assertEquals(excessiveDeposits.get(0).getAmount(), big("2000000"));
    assertEquals(excessiveDeposits.get(1).getUserId(), 2L);
    assertEquals(excessiveDeposits.get(1).getAmount(), big("3001000.0"));
  }
}
