package exchange.worker.worker;

import static org.assertj.core.api.Assertions.assertThat;

import exchange.common.constant.Currency;
import exchange.common.constant.TmsStatus;
import exchange.common.entity.Deposit;
import exchange.common.entity.FiatWithdrawal;
import exchange.common.entity.QuickCryptoToJpyUser;
import exchange.worker.worker.QuickCryptoToJpyChecker.ReadData;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.assertj.core.groups.Tuple;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@AutoConfigureMockMvc(addFilters = false)
@ActiveProfiles({"test"})
public class QuickCryptoToJpyCheckerProcessTest extends BaseProcessorTest {

  @Autowired
  QuickCryptoToJpyChecker checker;

  @DisplayName("データなしの場合")
  @Test
  void emptyTest() {
    // **************** 初期値を設定する ****************
    final var readData = new ReadData(
        new ArrayList<>(),
        new ArrayList<>(),
        new Date()
    );

    // **************** 処理を実行する ****************
    final var writeDtos = checker.process(readData);

    // **************** 結果を検証する ****************
    assertThat(writeDtos).as("出力データなし").isEmpty();
  }

  @DisplayName("日付境界テスト 検知対象外")
  @Test
  void doesNotExceedThresholdTest1() {
    // **************** 初期値を設定する ****************
    final var readData = new ReadData(
        List.of(generateDepositEntity(1L, 1L, Currency.ADA, "2022/02/01 00:00:00.000", "2022/02/01 00:00:00.000")),
        List.of(generateFiatWithdrawalEntity(1L, 1L, "2022/02/01 01:00:00.001")),
        new Date()
    );

    // **************** 処理を実行する ****************
    final var writeDtos = checker.process(readData);

    // **************** 結果を検証する ****************
    assertThat(writeDtos).as("出力データなし").isEmpty();
  }

  @DisplayName("日付境界テスト 検知対象外 入金よりも過去の時間")
  @Test
  void doesNotExceedThresholdTest2() {
    // **************** 初期値を設定する ****************
    final var readData = new ReadData(
        List.of(generateDepositEntity(1L, 1L, Currency.ADA, "2022/02/01 01:00:00.000", "2022/02/01 01:00:00.000")),
        List.of(
            generateFiatWithdrawalEntity(1L, 1L, "2022/02/01 00:00:00.000"),
            generateFiatWithdrawalEntity(2L, 1L, "2022/02/01 00:00:00.001"),
            generateFiatWithdrawalEntity(3L, 1L, "2022/02/01 00:59:59.999")
        ),
        new Date()
    );

    // **************** 処理を実行する ****************
    final var writeDtos = checker.process(readData);

    // **************** 結果を検証する ****************
    assertThat(writeDtos).as("出力データなし").isEmpty();
  }

  @DisplayName("日付境界テスト 検知対象内")
  @Test
  void exceedThresholdTest1() {
    // **************** 初期値を設定する ****************
    final var readData = new ReadData(
        List.of(generateDepositEntity(1L, 1L, Currency.ADA, "2022/02/01 00:00:00.000", "2022/02/01 00:00:00.000")),
        List.of(generateFiatWithdrawalEntity(1L, 1L, "2022/02/01 01:00:00.000")),
        new Date()
    );

    // **************** 処理を実行する ****************
    final var writeDtos = checker.process(readData);

    // **************** 結果を検証する ****************
    assertThat(writeDtos).as("出力データあり").isNotEmpty();
    assertThat(writeDtos)
        .as("targetId, userId, tmsStatus")
        .extracting(
            QuickCryptoToJpyUser::getTargetAt,
            QuickCryptoToJpyUser::getUserId,
            QuickCryptoToJpyUser::getTmsStatus
        )
        .containsExactly(
            Tuple.tuple(readData.dateFrom(), 1L, TmsStatus.OPEN)
        );
  }

  @DisplayName("2件目も対象外の場合")
  @Test
  void doesNotExceedThresholdTest3() {
    // **************** 初期値を設定する ****************
    final var readData = new ReadData(
        List.of(
            generateDepositEntity(1L, 1L, Currency.ADA, "2022/02/01 00:00:00.000", "2022/02/01 00:00:00.000"),
            generateDepositEntity(2L, 1L, Currency.ADA, "2022/02/01 00:00:00.001", "2022/02/01 00:00:00.001")
        ),
        List.of(
            generateFiatWithdrawalEntity(1L, 1L, "2022/02/01 01:00:00.002"),
            generateFiatWithdrawalEntity(2L, 1L, "2022/02/01 01:00:00.003")
        ),
        new Date()
    );

    // **************** 処理を実行する ****************
    final var writeDtos = checker.process(readData);

    // **************** 結果を検証する ****************
    assertThat(writeDtos).as("出力データなし").isEmpty();
  }

  @DisplayName("2件目以降が対象の場合")
  @Test
  void exceedThresholdTest2() {
    // **************** 初期値を設定する ****************
    final var readData = new ReadData(
        List.of(
            generateDepositEntity(1L, 1L, Currency.ADA, "2022/02/01 00:00:00.000", "2022/02/01 00:00:00.000"),
            generateDepositEntity(2L, 1L, Currency.ADA, "2022/02/01 00:00:00.001", "2022/02/01 00:00:00.001")
        ),
        List.of(
            generateFiatWithdrawalEntity(1L, 1L, "2022/02/01 01:00:00.001"),
            generateFiatWithdrawalEntity(2L, 1L, "2022/02/01 01:00:00.002")
        ),
        new Date()
    );

    // **************** 処理を実行する ****************
    final var writeDtos = checker.process(readData);

    // **************** 結果を検証する ****************
    assertThat(writeDtos).as("出力データあり").isNotEmpty();
    assertThat(writeDtos)
        .as("targetId, userId, tmsStatus")
        .extracting(
            QuickCryptoToJpyUser::getTargetAt,
            QuickCryptoToJpyUser::getUserId,
            QuickCryptoToJpyUser::getTmsStatus
        )
        .containsExactly(
            Tuple.tuple(readData.dateFrom(), 1L, TmsStatus.OPEN)
        );
  }

  @DisplayName("検知対象が複数のユーザーの場合")
  @Test
  void detectionMultipleUsersTest() {
    // **************** 初期値を設定する ****************
    final var readData = new ReadData(
        List.of(
            generateDepositEntity(1L, 1L, Currency.ADA, "2022/02/01 00:00:00.000", "2022/02/01 00:00:00.000"),
            generateDepositEntity(2L, 2L, Currency.ADA, "2022/02/01 00:00:00.000", "2022/02/01 00:00:00.000"),
            generateDepositEntity(3L, 2L, Currency.ADA, "2022/02/01 00:00:00.001", "2022/02/01 00:00:00.001"),
            generateDepositEntity(4L, 2L, Currency.ADA, "2022/02/01 00:00:00.002", "2022/02/01 00:00:00.002"),
            generateDepositEntity(5L, 3L, Currency.ADA, "2022/02/01 00:00:00.000", "2022/02/01 00:00:00.000"),
            generateDepositEntity(6L, 3L, Currency.ADA, "2022/02/01 00:00:00.001", "2022/02/01 00:00:00.001"),
            generateDepositEntity(7L, 3L, Currency.ADA, "2022/02/01 00:00:00.002", "2022/02/01 00:00:00.002"),
            generateDepositEntity(8L, 4L, Currency.ADA, "2022/02/01 00:00:00.000", "2022/02/01 00:00:00.000"),
            generateDepositEntity(9L, 4L, Currency.ADA, "2022/02/01 00:00:00.001", "2022/02/01 00:00:00.001")
        ),
        List.of(
            generateFiatWithdrawalEntity(1L, 1L, "2022/02/01 01:00:00.001"),
            generateFiatWithdrawalEntity(2L, 2L, "2022/02/01 01:00:00.001"),
            generateFiatWithdrawalEntity(3L, 3L, "2022/02/01 01:00:00.003"),
            generateFiatWithdrawalEntity(4L, 4L, "2022/02/01 01:00:00.001"),
            generateFiatWithdrawalEntity(5L, 4L, "2022/02/01 01:00:00.002")
        ),
        new Date()
    );

    // **************** 処理を実行する ****************
    final var writeDtos = checker.process(readData);

    // **************** 結果を検証する ****************
    assertThat(writeDtos).as("出力データあり").isNotEmpty();
    assertThat(writeDtos)
        .as("targetAt, userId, tmsStatus, depositCount")
        .extracting(
            QuickCryptoToJpyUser::getTargetAt,
            QuickCryptoToJpyUser::getUserId,
            QuickCryptoToJpyUser::getTmsStatus
        )
        .containsExactly(
            Tuple.tuple(readData.dateFrom(), 2L, TmsStatus.OPEN),
            Tuple.tuple(readData.dateFrom(), 4L, TmsStatus.OPEN)
        );
  }

  @DisplayName("使用日付データテスト depositはupdatedAtを見る")
  @Test
  void useDateCheckTest1() {
    // **************** 初期値を設定する ****************
    final var readData = new ReadData(
        List.of(generateDepositEntity(1L, 1L, Currency.ADA, "2000/01/02 00:00:00.000", "2022/02/01 00:00:00.000")),
        List.of(generateFiatWithdrawalEntity(1L, 1L, "2022/02/01 01:00:00.000")),
        new Date()
    );

    // **************** 処理を実行する ****************
    final var writeDtos = checker.process(readData);

    // **************** 結果を検証する ****************
    assertThat(writeDtos).as("出力データあり").isNotEmpty();
    assertThat(writeDtos)
        .as("targetId, userId, tmsStatus")
        .extracting(
            QuickCryptoToJpyUser::getTargetAt,
            QuickCryptoToJpyUser::getUserId,
            QuickCryptoToJpyUser::getTmsStatus
        )
        .containsExactly(
            Tuple.tuple(readData.dateFrom(), 1L, TmsStatus.OPEN)
        );
  }

  // utils
  private Deposit generateDepositEntity(
      final Long id,
      final Long userId,
      final Currency currency,
      final String createdAt,
      final String updatedAt) {
    final var entity = new Deposit();

    entity.setId(id);
    entity.setUserId(userId);
    entity.setCurrency(currency);
    entity.setCreatedAt(toDateSSS(createdAt));
    entity.setUpdatedAt(toDateSSS(updatedAt));

    return entity;
  }

  private FiatWithdrawal generateFiatWithdrawalEntity(final Long id, final Long userId,
      final String createdAt) {
    final var entity = new FiatWithdrawal();

    entity.setId(id);
    entity.setUserId(userId);
    entity.setCreatedAt(toDateSSS(createdAt));

    return entity;
  }
}
