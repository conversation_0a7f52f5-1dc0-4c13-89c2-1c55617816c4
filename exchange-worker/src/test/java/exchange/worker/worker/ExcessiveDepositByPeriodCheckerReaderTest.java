package exchange.worker.worker;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.SpyBean;
import exchange.common.constant.Currency;
import exchange.common.constant.DepositStatus;
import exchange.common.service.DepositService;
import exchange.common.service.ExcessiveDepositByPeriodService;
import exchange.spot.entity.SpotCandlestickAdaJpy;
import exchange.spot.service.SpotCandlestickAdaJpyService;

public class ExcessiveDepositByPeriodCheckerReaderTest extends BaseReaderTest {

  @Autowired
  ExcessiveDepositByPeriodService service;

  @SpyBean
  SpotCandlestickAdaJpyService spotCandlestickAdaJpyService;

  @SpyBean
  DepositService depositService;

  @BeforeEach
  public void beforeEach() {
    // 入金テーブルをリセットする
    executeSql(masterEM, "DELETE FROM deposit");
  }

  @DisplayName("read empty - zero deposits")
  @Test
  void readEmptyZeroDepositTest() { 
    var candlestick = new SpotCandlestickAdaJpy();
    candlestick.setClose(new BigDecimal("10"));
    doReturn(candlestick).when(spotCandlestickAdaJpyService).findOneByCondition(any(), any(), any(), any(), any(Boolean.class));

    var today = LocalDate.now();
    var readData = service.read(List.of(Currency.ADA), today);
    assertEquals(readData.date(), today);
    assertEquals(readData.depositsByCurrencies().get(0).deposits().size(), 0);
  }

  @DisplayName("read empty - boundary value of a date")
  @Test
  void readEmptyBoundaryDateTest() { 
    var candlestick = new SpotCandlestickAdaJpy();
    candlestick.setClose(new BigDecimal("10"));
    doReturn(candlestick).when(spotCandlestickAdaJpyService).findOneByCondition(any(), any(), any(), any(), any(Boolean.class));

    var today = LocalDate.parse("20230304", DateTimeFormatter.ofPattern("yyyyMMdd"));
    // date to string
    insertDeposit(masterEM, 1L, Currency.ADA, DepositStatus.DONE, "2023-02-25 23:59:59");
    insertDeposit(masterEM, 4L, Currency.ADA, DepositStatus.DONE, "2023-03-05 00:00:00"); 

    var readData = service.read(List.of(Currency.ADA), today);
    assertEquals(readData.date(), today);
    assertEquals(readData.depositsByCurrencies().get(0).deposits().size(), 0);
  }

  @DisplayName("read empty - other currency")
  @Test
  void readEmptyOtherCurrencyTest() { 
    var candlestick = new SpotCandlestickAdaJpy();
    candlestick.setClose(new BigDecimal("10"));
    doReturn(candlestick).when(spotCandlestickAdaJpyService).findOneByCondition(any(), any(), any(), any(), any(Boolean.class));

    var today = LocalDate.parse("20230304", DateTimeFormatter.ofPattern("yyyyMMdd"));
    // date to string
    insertDeposit(masterEM, 2L, Currency.JPY, DepositStatus.DONE, "2023-02-26 00:00:00"); 

    var readData = service.read(List.of(Currency.ADA), today);
    assertEquals(readData.date(), today);
    assertEquals(readData.depositsByCurrencies().get(0).deposits().size(), 0);
  }

  @DisplayName("read empty - other staus")
  @Test
  void readEmptyOtherStatusTest() { 
    var candlestick = new SpotCandlestickAdaJpy();
    candlestick.setClose(new BigDecimal("10"));
    doReturn(candlestick).when(spotCandlestickAdaJpyService).findOneByCondition(any(), any(), any(), any(), any(Boolean.class));

    var today = LocalDate.parse("20230304", DateTimeFormatter.ofPattern("yyyyMMdd"));
    // date to string
    insertDeposit(masterEM, 2L, Currency.JPY, DepositStatus.FOUND, "2023-02-26 00:00:00"); 
    insertDeposit(masterEM, 3L, Currency.JPY, DepositStatus.REJECTED, "2023-02-26 00:00:00"); 

    var readData = service.read(List.of(Currency.ADA), today);
    assertEquals(readData.date(), today);
    assertEquals(readData.depositsByCurrencies().get(0).deposits().size(), 0);
  }

  @DisplayName("read hit")
  @Test
  void readHitTest() { 
    // localdate from yyyymmdd
    var today = LocalDate.parse("20230304", DateTimeFormatter.ofPattern("yyyyMMdd"));

    // date to string
    insertDeposit(masterEM, 1L, Currency.ADA, DepositStatus.DONE, "2023-02-25 23:59:59"); 
    insertDeposit(masterEM, 2L, Currency.ADA, DepositStatus.DONE, "2023-02-26 00:00:00"); 
    insertDeposit(masterEM, 3L, Currency.ADA, DepositStatus.DONE, "2023-03-04 23:59:59"); 
    insertDeposit(masterEM, 4L, Currency.ADA, DepositStatus.DONE, "2023-03-05 00:00:00"); 

    var candlestick = new SpotCandlestickAdaJpy();
    candlestick.setClose(new BigDecimal("100000001"));
    doReturn(candlestick).when(spotCandlestickAdaJpyService).findOneByCondition(any(), any(), any(), any(), any(Boolean.class));

    
    var readData = service.read(List.of(Currency.ADA), today);
    assertEquals(readData.date(), today);
    assertEquals(readData.depositsByCurrencies().get(0).currency(), Currency.ADA);
    assertEquals(readData.depositsByCurrencies().get(0).jpyPrice(), new BigDecimal("100000001"));
    assertEquals(readData.depositsByCurrencies().get(0).deposits().size(), 2);
  }


  // ---- utility ----
  protected static BigDecimal big(String value) {
    return new BigDecimal(value);
  }

  // yyyy/MM/dd HH:mm:ss
  protected static Date toDate(String strDate) {
    final var zdt = ZonedDateTime.parse(
        strDate + "+0000",
        DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ssZ"));
    return Date.from(zdt.toInstant());
  }
}
